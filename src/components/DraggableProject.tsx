import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Project } from '../mockData';

interface DraggableProjectProps {
  project: Project;
  areaName?: string;
  children: React.ReactNode;
}

export default function DraggableProject({ project, areaName, children }: DraggableProjectProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ 
    id: project.id,
    data: {
      type: 'project',
      project,
      areaName,
    }
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1000 : 'auto',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className="touch-none"
    >
      <div
        {...listeners}
        className={`${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
      >
        {children}
      </div>
    </div>
  );
}
