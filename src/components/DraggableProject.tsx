import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Project } from '../mockData';

interface DraggableProjectProps {
  project: Project;
  areaName?: string;
  children: React.ReactNode;
}

export default function DraggableProject({ project, areaName, children }: DraggableProjectProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ 
    id: project.id,
    data: {
      type: 'project',
      project,
      areaName,
    }
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.8 : 1,
    zIndex: isDragging ? 1000 : 'auto',
    scale: isDragging ? '1.01' : '1',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className="touch-none"
    >
      <div
        {...listeners}
        className={`transition-all duration-200 ${
          isDragging
            ? 'cursor-grabbing shadow-2xl shadow-orange-500/20 ring-2 ring-orange-500/30 rounded-lg'
            : 'cursor-grab hover:shadow-lg'
        }`}
      >
        {children}
      </div>
    </div>
  );
}
