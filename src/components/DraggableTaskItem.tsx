import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Task, Project } from '../mockData';
import TaskItem from './TaskItem';

interface DraggableTaskItemProps {
  task: Task;
  project?: Project;
  areaName?: string;
  showTodayStar?: boolean;
  showInLogbook?: boolean;
  onToggleChecklist: (
    projectId: string,
    taskId: string,
    checklistId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onToggleTask: (
    projectId: string,
    taskId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onEditTask?: (task: Task, projectId: string, areaName?: string) => void;
}

export default function DraggableTaskItem(props: DraggableTaskItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ 
    id: props.task.id,
    data: {
      type: 'task',
      task: props.task,
      projectId: props.project?.id,
      areaName: props.areaName,
    }
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1000 : 'auto',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`touch-none ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
    >
      <TaskItem {...props} />
    </div>
  );
}
