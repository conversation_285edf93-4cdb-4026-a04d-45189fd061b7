import React from "react";
import Modal from "./Modal";

export default function NewListModal({ open, onClose, onNewArea, onNewProject }:{ open:boolean, onClose:()=>void, onNewArea?:()=>void, onNewProject?:()=>void }) {
  return (
    <Modal open={open} onClose={onClose}>
      <div className="bg-[#292929] p-0 rounded-lg shadow-lg w-80 relative text-left">
        <button className="absolute top-2 right-2 text-gray-400 hover:text-gray-100 text-2xl" onClick={onClose}>×</button>
        <div className="divide-y divide-gray-700">
          <button
            className="flex items-start gap-3 w-full px-6 py-4 bg-transparent hover:bg-[#232323] focus:bg-[#232323] transition rounded-t-lg outline-none"
            onClick={() => { if (onClose) onClose(); if (onNewProject) onNewProject(); }}
          >
            <span className="mt-1 text-blue-400 text-xl">⭕</span>
            <span className="flex flex-col items-start">
              <span className="font-semibold text-gray-100 text-base">New Project</span>
              <span className="text-xs text-gray-400">Define a goal, then work towards it one to-do at a time.</span>
            </span>
          </button>
          <button
            className="flex items-start gap-3 w-full px-6 py-4 bg-transparent hover:bg-[#232323] focus:bg-[#232323] transition rounded-b-lg outline-none"
            onClick={() => { if (onClose) onClose(); if (onNewArea) onNewArea(); }}
          >
            <span className="mt-1 text-green-400 text-xl">🗃️</span>
            <span className="flex flex-col items-start">
              <span className="font-semibold text-gray-100 text-base">New Area</span>
              <span className="text-xs text-gray-400">Group your projects and to-dos based on different responsibilities, such as Family or Work.</span>
            </span>
          </button>
        </div>
      </div>
    </Modal>
  );
}
