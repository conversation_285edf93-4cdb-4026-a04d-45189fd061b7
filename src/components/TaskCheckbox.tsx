import React from "react";

interface TaskCheckboxProps {
  checked: boolean;
  onClick: () => void;
  ariaLabel?: string;
  disabled?: boolean;
}

const TaskCheckbox: React.FC<TaskCheckboxProps> = ({ checked, onClick, ariaLabel, disabled }) => (
  <button
    type="button"
    role="checkbox"
    aria-checked={checked}
    aria-label={ariaLabel}
    tabIndex={0}
    disabled={disabled}
    className={`mr-2 flex items-center justify-center rounded-full border-2 transition-all duration-150 text-sm ${checked ? 'bg-[var(--sidebar-selected,#f59e42)] border-[var(--sidebar-selected,#f59e42)]' : 'bg-transparent border-gray-400 hover:border-white'} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    style={{ width: '1.1em', height: '1.1em', minWidth: '1.1em', minHeight: '1.1em', lineHeight: 1, padding: 0 }}
    onClick={onClick}
  >
    {checked ? (
      <svg
        className="text-white"
        fill="none"
        stroke="currentColor"
        strokeWidth="2.5"
        viewBox="0 0 20 20"
        style={{ width: '1em', height: '1em', display: 'block' }}
      >
        <polyline points="5 11 9 15 15 7" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
    ) : null}
  </button>
);

export default TaskCheckbox;
