import React, { useState, useEffect } from 'react';
import { Area } from '../mockData';
import Modal from './Modal';

interface AreaModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (area: Omit<Area, 'id' | 'projects'>) => void;
  area?: Area; // For editing existing area
}

export default function AreaModal({ 
  open, 
  onClose, 
  onSave, 
  area 
}: AreaModalProps) {
  const [name, setName] = useState('');

  // Reset form when modal opens/closes or area changes
  useEffect(() => {
    if (open) {
      if (area) {
        // Editing existing area
        setName(area.name);
      } else {
        // Creating new area
        setName('');
      }
    }
  }, [open, area]);

  const handleSave = () => {
    if (!name.trim()) return;

    const areaData: Omit<Area, 'id' | 'projects'> = {
      name: name.trim(),
    };

    onSave(areaData);
    onClose();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave();
    }
  };

  return (
    <Modal open={open} onClose={onClose}>
      <div className="bg-[#2a2a2a] rounded-lg p-6 w-full max-w-md mx-auto">
        <h2 className="text-xl font-bold text-white mb-4">
          {area ? 'Edit Area' : 'New Area'}
        </h2>

        {/* Name */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Area Name *
          </label>
          <input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            onKeyPress={handleKeyPress}
            className="w-full px-3 py-2 bg-[#1a1a1a] border border-gray-600 rounded text-white focus:outline-none focus:border-orange-500"
            placeholder="Enter area name..."
            autoFocus
          />
          <div className="mt-2 text-sm text-gray-400">
            Areas represent broad life categories like "Work", "Personal", or "Home".
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-300 hover:text-white"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={!name.trim()}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {area ? 'Save Changes' : 'Create Area'}
          </button>
        </div>
      </div>
    </Modal>
  );
}
