import React from "react";

export default function SidebarFooter({ onNewList, onSettings }:{ onNewList:()=>void, onSettings:()=>void }) {
  return (
    <div className="w-full flex items-center justify-between px-4 py-3 border-t border-gray-800 bg-black">
      <button onClick={onNewList} className="flex items-center gap-2 text-gray-100 hover:text-primary font-medium">
        <span className="text-xl">＋</span>
        <span>New List</span>
      </button>
      <button onClick={onSettings} className="text-gray-100 hover:text-secondary text-xl">
        <span>⚙️</span>
      </button>
    </div>
  );
}
