import React from 'react';
import { Task, Project } from '../../mockData';
import TaskGroup from '../shared/TaskGroup';
import EmptyState from '../shared/EmptyState';
import { isTodayTask } from '../../utils/date';

interface FilterTaskGroupsProps {
  filteredTasks: (Task & { project?: Project; areaName?: string })[];
  selectedKey: string;
  onToggleChecklist: (
    projectId: string,
    taskId: string,
    checklistId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onToggleTask: (
    projectId: string,
    taskId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
}

export default function FilterTaskGroups({ 
  filteredTasks, 
  selectedKey, 
  onToggleChecklist, 
  onToggleTask 
}: FilterTaskGroupsProps) {
  // Group tasks by project
  const grouped: Record<string, { project?: Project; areaName?: string; tasks: (Task & {project?: Project, areaName?: string})[] }> = {};
  
  filteredTasks.forEach(t => {
    let key = 'Inbox';
    if (t.project) {
      key = t.areaName ? `${t.areaName}::${t.project.name}` : t.project.name;
    }
    if (!grouped[key]) grouped[key] = { project: t.project, areaName: t.areaName, tasks: [] };
    grouped[key].tasks.push(t);
  });

  const projectKeys = Object.keys(grouped);

  if (projectKeys.length === 0) {
    return <EmptyState message="No tasks match this filter." />;
  }

  return (
    <>
      {projectKeys.map(key => {
        const group = grouped[key];
        const title = group.project
          ? (group.areaName ? `${group.areaName} / ` : '') + group.project.name
          : 'Inbox';
        
        return (
          <TaskGroup
            key={key}
            title={title}
            tasks={group.tasks}
            showTodayStar={selectedKey === 'filter:Anytime'}
            showInLogbook={selectedKey === 'filter:Logbook'}
            onToggleChecklist={onToggleChecklist}
            onToggleTask={onToggleTask}
          />
        );
      })}
    </>
  );
}
