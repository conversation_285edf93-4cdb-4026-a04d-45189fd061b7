import React from 'react';
import { Task, Project } from '../../mockData';
import TaskGroup from '../shared/TaskGroup';
import EmptyState from '../shared/EmptyState';

interface TodayFilterViewProps {
  filteredTasks: (Task & { project?: Project; areaName?: string })[];
  onToggleChecklist: (
    projectId: string,
    taskId: string,
    checklistId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onToggleTask: (
    projectId: string,
    taskId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
}

export default function TodayFilterView({ 
  filteredTasks, 
  onToggleChecklist, 
  onToggleTask 
}: TodayFilterViewProps) {
  const regularTasks = filteredTasks.filter(t => !t.thisEvening);
  const eveningTasks = filteredTasks.filter(t => t.thisEvening);

  // Group regular tasks
  const grouped: Record<string, { project?: Project; areaName?: string; tasks: (Task & {project?: Project, areaName?: string})[] }> = {};
  regularTasks.forEach(t => {
    let key = 'Inbox';
    if (t.project) {
      key = t.areaName ? `${t.areaName}::${t.project.name}` : t.project.name;
    }
    if (!grouped[key]) grouped[key] = { project: t.project, areaName: t.areaName, tasks: [] };
    grouped[key].tasks.push(t);
  });

  // Group evening tasks
  const eveningGrouped: Record<string, { project?: Project; areaName?: string; tasks: (Task & {project?: Project, areaName?: string})[] }> = {};
  eveningTasks.forEach(t => {
    let key = 'Inbox';
    if (t.project) {
      key = t.areaName ? `${t.areaName}::${t.project.name}` : t.project.name;
    }
    if (!eveningGrouped[key]) eveningGrouped[key] = { project: t.project, areaName: t.areaName, tasks: [] };
    eveningGrouped[key].tasks.push(t);
  });

  const projectKeys = Object.keys(grouped);
  const eveningKeys = Object.keys(eveningGrouped);

  return (
    <div className="p-8">
      <h2 className="text-2xl font-bold mb-4 text-white">Today</h2>

      {/* Regular Today tasks */}
      {projectKeys.length === 0 && eveningKeys.length === 0 && (
        <EmptyState message="No tasks for today." />
      )}
      
      {projectKeys.map(key => {
        const group = grouped[key];
        const title = group.project
          ? (group.areaName ? `${group.areaName} / ` : '') + group.project.name
          : 'Inbox';
        
        return (
          <TaskGroup
            key={key}
            title={title}
            tasks={group.tasks}
            onToggleChecklist={onToggleChecklist}
            onToggleTask={onToggleTask}
          />
        );
      })}

      {/* This Evening section */}
      {eveningKeys.length > 0 && (
        <div className="mt-12">
          <h3 className="text-xl font-semibold mb-4 text-gray-300">This Evening</h3>
          {eveningKeys.map(key => {
            const group = eveningGrouped[key];
            const title = group.project
              ? (group.areaName ? `${group.areaName} / ` : '') + group.project.name
              : 'Inbox';
            
            return (
              <TaskGroup
                key={`evening-${key}`}
                title={title}
                tasks={group.tasks}
                onToggleChecklist={onToggleChecklist}
                onToggleTask={onToggleTask}
              />
            );
          })}
        </div>
      )}
    </div>
  );
}
