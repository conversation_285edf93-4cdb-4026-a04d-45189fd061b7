'use client';
import React, { useState } from "react";
import SidebarFilters from "./SidebarFilters";
// selectedKey: string, onSelect: (key: string) => void
import SidebarProjects from "./SidebarProjects";
import SidebarAreas from "./SidebarAreas";
import SidebarFooter from "./SidebarFooter";
import NewListModal from "./NewListModal";
import SettingsModal from "./SettingsModal";

import { Area, Project } from "../mockData";

interface SidebarProps {
  areas: Area[];
  projects: Project[];
  selectedKey: string;
  onSelect: (key: string) => void;
  onAddArea: () => void;
  onAddProject: () => void;
  onAreaNameChange: (areaKey: string, newName: string) => void;
  filterCounts: { [key: string]: number };
}

export default function Sidebar({
  areas,
  projects,
  selectedKey,
  onSelect,
  onAddArea,
  onAddProject,
  onAreaNameChange,
  filterCounts
}: SidebarProps) {
  const [showNewList, setShowNewList] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  return (
    <aside className="relative w-64 h-screen bg-[#222120] border-r border-gray-900 flex flex-col">
      <div className="flex-1 overflow-y-auto p-4 flex flex-col gap-4">
        <SidebarFilters selectedKey={selectedKey} onSelect={onSelect} filterCounts={filterCounts} />
        <SidebarProjects projects={projects} selectedKey={selectedKey} onSelect={onSelect} />
        <hr className="my-4 border-gray-700" />
        <SidebarAreas
          areas={areas}
          selectedKey={selectedKey}
          onSelect={onSelect}
        />
      </div>
      <SidebarFooter onNewList={() => setShowNewList(true)} onSettings={() => setShowSettings(true)} />
      <NewListModal open={showNewList} onClose={() => setShowNewList(false)} onNewArea={() => { setShowNewList(false); onAddArea(); }} onNewProject={() => { setShowNewList(false); onAddProject(); }} />
      <SettingsModal open={showSettings} onClose={() => setShowSettings(false)} />
    </aside>
  );
}
