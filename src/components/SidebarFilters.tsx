import React from "react";

const filters = [
  { label: "Inbox", icon: "📧" },
  { label: "Today", icon: "⭐" },
  { label: "Upcoming", icon: "🗓️" },
  { label: "Anytime", icon: "📚" },
  { label: "Someday", icon: "🕰️" },
  { label: "Logbook", icon: "📔" },
];

type FilterCounts = { [key: string]: number };

export default function SidebarFilters({selectedKey, onSelect, filterCounts}:{selectedKey:string, onSelect:(key:string)=>void, filterCounts: FilterCounts}) {
  return (
    <ul className="flex flex-col gap-1 mt-4">
      {filters.map((filter) => {
        const key = `filter:${filter.label}`;
        const selected = selectedKey === key;
        return (
          <li
            key={filter.label}
            className={`flex items-center justify-between px-4 py-2 rounded cursor-pointer transition-colors group hover:bg-[#363636]`}
            onClick={() => onSelect(key)}
          >
            <span className="flex items-center gap-2">
              {/* Show yellow star for Today, otherwise default icon */}
              {filter.label === "Today" ? (
                <span className="text-lg" style={{ color: '#fff700' }}>★</span>
              ) : (
                <span className="text-lg">{filter.icon}</span>
              )}
              <span
                className={
                  selected
                    ? "text-[var(--sidebar-selected, #f59e42)] font-semibold"
                    : "text-sm text-gray-100 group-hover:text-white"
                }
                style={selected ? { color: '#f59e42', fontWeight: 600 } : {}}
              >
                {filter.label}
              </span>
            </span>
            <span
              className={
                selected
                  ? "text-[var(--sidebar-selected, #f59e42)] font-semibold font-mono"
                  : "text-xs text-gray-400 group-hover:text-white font-mono"
              }
              style={selected ? { color: '#f59e42', fontWeight: 600 } : {}}
            >
              {filterCounts[`filter:${filter.label}`] ?? 0}
            </span>
          </li>
        );
      })}
    </ul>
  );
}
