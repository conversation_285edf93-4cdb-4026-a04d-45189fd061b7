import React from 'react';
import { Project } from '../mockData';

interface ProjectProgressIndicatorProps {
  project: Project;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export default function ProjectProgressIndicator({ 
  project, 
  size = 'md', 
  className = '' 
}: ProjectProgressIndicatorProps) {
  const totalTasks = project.tasks.length;
  const completedTasks = project.tasks.filter(task => task.completed).length;
  const progressPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
  const isComplete = progressPercentage === 100 && totalTasks > 0;

  // Size configurations
  const sizeConfig = {
    sm: { 
      container: 'w-4 h-4', 
      stroke: '2', 
      checkSize: 'w-2.5 h-2.5',
      textSize: 'text-xs'
    },
    md: { 
      container: 'w-5 h-5', 
      stroke: '2.5', 
      checkSize: 'w-3 h-3',
      textSize: 'text-sm'
    },
    lg: { 
      container: 'w-6 h-6', 
      stroke: '3', 
      checkSize: 'w-4 h-4',
      textSize: 'text-base'
    }
  };

  const config = sizeConfig[size];
  const radius = 8; // SVG circle radius
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (progressPercentage / 100) * circumference;

  if (totalTasks === 0) {
    // Empty project - show empty circle
    return (
      <div className={`${config.container} ${className} flex items-center justify-center`}>
        <div className={`${config.container} rounded-full border-2 border-gray-400`} />
      </div>
    );
  }

  return (
    <div className={`${config.container} ${className} relative flex items-center justify-center`}>
      {isComplete ? (
        // Show checkmark when complete
        <div className={`${config.container} rounded-full bg-orange-500 border-2 border-orange-500 flex items-center justify-center`}>
          <svg
            className={`${config.checkSize} text-white`}
            fill="none"
            stroke="currentColor"
            strokeWidth={config.stroke}
            viewBox="0 0 20 20"
          >
            <polyline 
              points="5 11 9 15 15 7" 
              strokeLinecap="round" 
              strokeLinejoin="round" 
            />
          </svg>
        </div>
      ) : (
        // Show progress circle
        <div className="relative">
          <svg
            className={config.container}
            viewBox="0 0 20 20"
            style={{ transform: 'rotate(-90deg)' }}
          >
            {/* Background circle */}
            <circle
              cx="10"
              cy="10"
              r={radius}
              stroke="rgb(156, 163, 175)" // gray-400
              strokeWidth={config.stroke}
              fill="transparent"
              className="opacity-30"
            />
            {/* Progress circle */}
            <circle
              cx="10"
              cy="10"
              r={radius}
              stroke="rgb(249, 115, 22)" // orange-500
              strokeWidth={config.stroke}
              fill="transparent"
              strokeDasharray={circumference}
              strokeDashoffset={strokeDashoffset}
              strokeLinecap="round"
              className="transition-all duration-300 ease-in-out"
            />
          </svg>
          {/* Progress percentage text */}
          <div className="absolute inset-0 flex items-center justify-center">
            <span className={`${config.textSize} text-gray-300 font-medium`}>
              {Math.round(progressPercentage)}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
