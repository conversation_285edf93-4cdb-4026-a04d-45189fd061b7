import React from 'react';
import { Project, Task } from '../../mockData';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import EmptyState from '../shared/EmptyState';
import ProjectTaskList from '../project/ProjectTaskList';
import ProjectProgressIndicator from '../ProjectProgressIndicator';
import DraggableProject from '../DraggableProject';
import DroppableProject from '../DroppableProject';

interface ProjectsViewProps {
  projects: Project[];
  onToggleChecklist: (
    projectId: string,
    taskId: string,
    checklistId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onToggleTask: (
    projectId: string,
    taskId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onAddTask?: (projectId: string, areaName?: string) => void;
  onEditTask?: (task: Task, projectId: string, areaName?: string) => void;
  onAddProject?: (areaName?: string) => void;
  onEditProject?: (project: Project, areaName?: string) => void;
}

export default function ProjectsView({ 
  projects,
  onToggleChecklist, 
  onToggleTask,
  onAddTask,
  onEditTask,
  onAddProject,
  onEditProject
}: ProjectsViewProps) {
  return (
    <div className="p-8">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold text-white">Projects</h2>
        {onAddProject && (
          <button
            onClick={() => onAddProject()}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
          >
            + New Project
          </button>
        )}
      </div>
      
      {projects.length === 0 ? (
        <EmptyState message="No projects yet. Create your first project!" />
      ) : (
        <SortableContext items={projects.map(p => p.id)} strategy={verticalListSortingStrategy}>
          <div className="space-y-8">
            {projects.map(project => (
              <DraggableProject key={project.id} project={project}>
                <DroppableProject
                  project={project}
                  className="relative mb-8"
                >
                  <div className="mb-8">
                    <div className="flex items-center gap-3 mb-2">
                      <ProjectProgressIndicator project={project} size="md" />
                      <div className="font-semibold text-lg text-orange-400 flex-1">{project.name}</div>
                      <div className="flex gap-2">
                        {onAddTask && (
                          <button
                            onClick={() => onAddTask(project.id)}
                            className="px-2 py-1 text-xs bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
                          >
                            + Task
                          </button>
                        )}
                        {onEditProject && (
                          <button
                            onClick={() => onEditProject(project)}
                            className="px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded hover:bg-gray-600 hover:text-white transition-colors"
                          >
                            Edit
                          </button>
                        )}
                      </div>
                    </div>
                    {project.notes && (
                      <div className="mb-4 text-gray-300 text-sm bg-[#232323] p-3 rounded">
                        {project.notes}
                      </div>
                    )}
                    {project.tasks.length === 0 ? (
                      <EmptyState message="No tasks in this project yet." className="text-gray-400 text-sm" />
                    ) : (
                      <ProjectTaskList
                        project={project}
                        onToggleChecklist={onToggleChecklist}
                        onToggleTask={onToggleTask}
                        onEditTask={onEditTask}
                      />
                    )}
                  </div>
                </DroppableProject>
              </DraggableProject>
            ))}
          </div>
        </SortableContext>
      )}
    </div>
  );
}
