import React from 'react';
import { Project } from '../../mockData';
import ProjectHeader from '../project/ProjectHeader';
import ProjectTaskList from '../project/ProjectTaskList';

interface ProjectViewProps {
  project: Project;
  areaName?: string;
  onToggleChecklist: (
    projectId: string,
    taskId: string,
    checklistId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onToggleTask: (
    projectId: string,
    taskId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
}

export default function ProjectView({ 
  project, 
  areaName, 
  onToggleChecklist, 
  onToggleTask 
}: ProjectViewProps) {
  return (
    <div className="p-8">
      <ProjectHeader project={project} areaName={areaName} />
      <ProjectTaskList 
        project={project} 
        areaName={areaName}
        onToggleChecklist={onToggleChecklist}
        onToggleTask={onToggleTask}
      />
    </div>
  );
}
