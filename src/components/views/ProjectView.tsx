import React from 'react';
import { Project, Task } from '../../mockData';
import ProjectHeader from '../project/ProjectHeader';
import ProjectTaskList from '../project/ProjectTaskList';
import DroppableProject from '../DroppableProject';

interface ProjectViewProps {
  project: Project;
  areaName?: string;
  onToggleChecklist: (
    projectId: string,
    taskId: string,
    checklistId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onToggleTask: (
    projectId: string,
    taskId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onAddTask?: (projectId: string, areaName?: string) => void;
  onEditTask?: (task: Task, projectId: string, areaName?: string) => void;
  onEditProject?: (project: Project, areaName?: string) => void;
}

export default function ProjectView({
  project,
  areaName,
  onToggleChecklist,
  onToggleTask,
  onAddTask,
  onEditTask,
  onEditProject
}: ProjectViewProps) {
  return (
    <div className="p-8">
      <ProjectHeader
        project={project}
        areaName={areaName}
        onEditProject={onEditProject}
      />

      {/* Add Task Button */}
      {onAddTask && (
        <div className="mb-6">
          <button
            onClick={() => onAddTask(project.id, areaName)}
            className="flex items-center gap-2 px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
          >
            <span>+</span>
            Add Task
          </button>
        </div>
      )}

      <DroppableProject
        project={project}
        areaName={areaName}
        className="relative"
      >
        <ProjectTaskList
          project={project}
          areaName={areaName}
          onToggleChecklist={onToggleChecklist}
          onToggleTask={onToggleTask}
          onEditTask={onEditTask}
        />
      </DroppableProject>
    </div>
  );
}
