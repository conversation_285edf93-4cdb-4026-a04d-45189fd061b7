import React from 'react';
import { Task, Project } from '../../mockData';
import { getFilterDefinition } from '../../utils/filterDefinitions';
import FilterTaskGroups from '../filter/FilterTaskGroups';
import TodayFilterView from '../filter/TodayFilterView';

interface FilterViewProps {
  selectedKey: string;
  allTasks: (Task & { project: Project; areaName?: string })[];
  projects: Project[];
  onToggleChecklist: (
    projectId: string,
    taskId: string,
    checklistId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onToggleTask: (
    projectId: string,
    taskId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onAddTask?: (projectId: string, areaName?: string) => void;
}

export default function FilterView({
  selectedKey,
  allTasks,
  projects,
  onToggleChecklist,
  onToggleTask,
  onAddTask
}: FilterViewProps) {
  const filterDef = getFilterDefinition(selectedKey);
  
  if (!filterDef) {
    return (
      <div className="p-8">
        <h2 className="text-2xl font-bold mb-4 text-white">Unknown Filter</h2>
        <div className="text-gray-400">The selected filter could not be found.</div>
      </div>
    );
  }

  let filteredTasks: (Task & { project?: Project; areaName?: string })[] = [];

  // Special handling for Inbox
  if (filterDef.specialHandling === 'inbox') {
    const inboxProject = projects.find(p => p.name === 'Inbox');
    filteredTasks = inboxProject
      ? inboxProject.tasks.filter(task => !task.completed).map(task => ({ ...task, project: inboxProject }))
      : [];
  } else {
    // Apply filter function
    filteredTasks = allTasks.filter(filterDef.filterFn);
  }

  // Special handling for Today filter
  if (filterDef.specialHandling === 'today') {
    return (
      <TodayFilterView
        filteredTasks={filteredTasks}
        onToggleChecklist={onToggleChecklist}
        onToggleTask={onToggleTask}
      />
    );
  }

  // Regular filter view
  return (
    <div className="p-8">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold text-white">{filterDef.label}</h2>
        {filterDef.specialHandling === 'inbox' && onAddTask && (
          <button
            onClick={() => {
              const inboxProject = projects.find(p => p.name === 'Inbox');
              if (inboxProject) {
                onAddTask(inboxProject.id);
              }
            }}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
          >
            + Add to Inbox
          </button>
        )}
      </div>
      <FilterTaskGroups
        filteredTasks={filteredTasks}
        selectedKey={selectedKey}
        onToggleChecklist={onToggleChecklist}
        onToggleTask={onToggleTask}
      />
    </div>
  );
}
