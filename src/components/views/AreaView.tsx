import React from 'react';
import { Area, Project, Task } from '../../mockData';
import EmptyState from '../shared/EmptyState';
import ProjectTaskList from '../project/ProjectTaskList';
import ProjectProgressIndicator from '../ProjectProgressIndicator';

interface AreaViewProps {
  area: Area;
  onToggleChecklist: (
    projectId: string,
    taskId: string,
    checklistId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onToggleTask: (
    projectId: string,
    taskId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onAddTask?: (projectId: string, areaName?: string) => void;
  onEditTask?: (task: Task, projectId: string, areaName?: string) => void;
  onAddProject?: (areaName?: string) => void;
  onEditProject?: (project: Project, areaName?: string) => void;
  onEditArea?: (area: Area) => void;
}

export default function AreaView({
  area,
  onToggleChecklist,
  onToggleTask,
  onAddTask,
  onEditTask,
  onAddProject,
  onEditProject,
  onEditArea
}: AreaViewProps) {
  return (
    <div className="p-8">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold text-white">{area.name}</h2>
        <div className="flex gap-2">
          {onAddProject && (
            <button
              onClick={() => onAddProject(area.name)}
              className="px-3 py-1 text-sm bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
            >
              + Project
            </button>
          )}
          {onEditArea && (
            <button
              onClick={() => onEditArea(area)}
              className="px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600 hover:text-white transition-colors"
            >
              Edit Area
            </button>
          )}
        </div>
      </div>
      
      {area.projects.length === 0 ? (
        <EmptyState message="No projects in this area yet." />
      ) : (
        <div className="space-y-8">
          {area.projects.map(project => (
            <div key={project.id} className="mb-8">
              <div className="flex items-center gap-3 mb-2">
                <ProjectProgressIndicator project={project} size="md" />
                <div className="font-semibold text-lg text-orange-400 flex-1">{project.name}</div>
                <div className="flex gap-2">
                  {onAddTask && (
                    <button
                      onClick={() => onAddTask(project.id, area.name)}
                      className="px-2 py-1 text-xs bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
                    >
                      + Task
                    </button>
                  )}
                  {onEditProject && (
                    <button
                      onClick={() => onEditProject(project, area.name)}
                      className="px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded hover:bg-gray-600 hover:text-white transition-colors"
                    >
                      Edit
                    </button>
                  )}
                </div>
              </div>
              {project.notes && (
                <div className="mb-4 text-gray-300 text-sm bg-[#232323] p-3 rounded">
                  {project.notes}
                </div>
              )}
              {project.tasks.length === 0 ? (
                <EmptyState message="No tasks in this project yet." className="text-gray-400 text-sm" />
              ) : (
                <ProjectTaskList
                  project={project}
                  areaName={area.name}
                  onToggleChecklist={onToggleChecklist}
                  onToggleTask={onToggleTask}
                  onEditTask={onEditTask}
                />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
