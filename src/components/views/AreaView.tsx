import React from 'react';
import { Area } from '../../mockData';
import EmptyState from '../shared/EmptyState';
import ProjectTaskList from '../project/ProjectTaskList';
import ProjectProgressIndicator from '../ProjectProgressIndicator';

interface AreaViewProps {
  area: Area;
  onToggleChecklist: (
    projectId: string,
    taskId: string,
    checklistId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onToggleTask: (
    projectId: string,
    taskId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
}

export default function AreaView({ area, onToggleChecklist, onToggleTask }: AreaViewProps) {
  return (
    <div className="p-8">
      <h2 className="text-2xl font-bold mb-4 text-white">{area.name}</h2>
      
      {area.projects.length === 0 ? (
        <EmptyState message="No projects in this area yet." />
      ) : (
        <div className="space-y-8">
          {area.projects.map(project => (
            <div key={project.id} className="mb-8">
              <div className="flex items-center gap-3 mb-2">
                <ProjectProgressIndicator project={project} size="md" />
                <div className="font-semibold text-lg text-orange-400">{project.name}</div>
              </div>
              {project.notes && (
                <div className="mb-4 text-gray-300 text-sm bg-[#232323] p-3 rounded">
                  {project.notes}
                </div>
              )}
              {project.tasks.length === 0 ? (
                <EmptyState message="No tasks in this project yet." className="text-gray-400 text-sm" />
              ) : (
                <ProjectTaskList 
                  project={project} 
                  areaName={area.name}
                  onToggleChecklist={onToggleChecklist}
                  onToggleTask={onToggleTask}
                />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
