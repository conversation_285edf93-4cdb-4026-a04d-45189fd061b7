'use client';
import React from "react";
import { Area, Project, Task } from "../mockData";
import ProjectView from "./views/ProjectView";
import AreaView from "./views/AreaView";
import FilterView from "./views/FilterView";
import ProjectsView from "./views/ProjectsView";
import WelcomeView from "./views/WelcomeView";

interface MainContentProps {
  selectedKey: string;
  areas: Area[];
  projects: Project[];
  onToggleChecklist: (
    projectId: string,
    taskId: string,
    checklistId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onToggleTask: (
    projectId: string,
    taskId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onAddTask?: (projectId: string, areaName?: string) => void;
  onEditTask?: (task: Task, projectId: string, areaName?: string) => void;
  onAddProject?: (areaName?: string) => void;
  onEditProject?: (project: Project, areaName?: string) => void;
  onAddArea?: () => void;
  onEditArea?: (area: Area) => void;
}

export default function MainContent({
  selectedKey,
  areas,
  projects,
  onToggleChecklist,
  onToggleTask,
  onAddTask,
  onEditTask,
  onAddProject,
  onEditProject,
  onAddArea,
  onEditArea
}: MainContentProps) {
  // PROJECTS VIEW: If projects view is selected
  if (selectedKey === 'view:projects') {
    return (
      <ProjectsView
        projects={projects}
        onToggleChecklist={onToggleChecklist}
        onToggleTask={onToggleTask}
        onAddTask={onAddTask}
        onEditTask={onEditTask}
        onAddProject={onAddProject}
        onEditProject={onEditProject}
      />
    );
  }

  // PROJECT LOGIC: If a standalone project is selected
  if (selectedKey.startsWith('project:')) {
    const projectName = selectedKey.slice(8); // Remove 'project:' prefix
    const project = projects.find(p => p.name === projectName);

    if (!project) {
      return (
        <div className="p-8">
          <h2 className="text-2xl font-bold mb-4 text-white">Project Not Found</h2>
          <div className="text-gray-400">The selected project could not be found.</div>
        </div>
      );
    }

    return (
      <ProjectView
        project={project}
        onToggleChecklist={onToggleChecklist}
        onToggleTask={onToggleTask}
        onAddTask={onAddTask}
        onEditTask={onEditTask}
        onEditProject={onEditProject}
      />
    );
  }

  // AREA LOGIC: If an area or area project is selected
  if (selectedKey.startsWith('area:')) {
    const areaKey = selectedKey.slice(5); // Remove 'area:' prefix

    // Check if it's a specific project within an area
    const area = areas.find(a => areaKey.startsWith(a.name));
    if (!area) {
      return (
        <div className="p-8">
          <h2 className="text-2xl font-bold mb-4 text-white">Area Not Found</h2>
          <div className="text-gray-400">The selected area could not be found.</div>
        </div>
      );
    }

    // Check if it's a specific project within the area
    if (areaKey.length > area.name.length) {
      const projectName = areaKey.slice(area.name.length + 1); // +1 for the space
      const project = area.projects.find(p => p.name === projectName);

      if (!project) {
        return (
          <div className="p-8">
            <h2 className="text-2xl font-bold mb-4 text-white">Project Not Found</h2>
            <div className="text-gray-400">The selected project could not be found in {area.name}.</div>
          </div>
        );
      }

      return (
        <ProjectView
          project={project}
          areaName={area.name}
          onToggleChecklist={onToggleChecklist}
          onToggleTask={onToggleTask}
          onAddTask={onAddTask}
          onEditTask={onEditTask}
          onEditProject={onEditProject}
        />
      );
    }

    // It's just the area itself - show all projects in the area
    return (
      <AreaView
        area={area}
        onToggleChecklist={onToggleChecklist}
        onToggleTask={onToggleTask}
        onAddTask={onAddTask}
        onEditTask={onEditTask}
        onAddProject={onAddProject}
        onEditProject={onEditProject}
        onEditArea={onEditArea}
      />
    );
  }

  // FILTER LOGIC: If a filter is selected, show matching tasks grouped by project
  if (selectedKey.startsWith('filter:')) {
    // Helper: get all projects (standalone + in areas)
    const allProjects: { project: Project; areaName?: string }[] = [
      ...projects.map(p => ({ project: p })),
      ...areas.flatMap(area => area.projects.map(p => ({ project: p, areaName: area.name })))
    ];
    // Helper: get all tasks with project/area context
    const allTasks = allProjects.flatMap(({ project, areaName }) =>
      project.tasks.map(task => ({ ...task, project, areaName }))
    );

    return (
      <FilterView
        selectedKey={selectedKey}
        allTasks={allTasks}
        projects={projects}
        onToggleChecklist={onToggleChecklist}
        onToggleTask={onToggleTask}
        onAddTask={onAddTask}
        onEditTask={onEditTask}
      />
    );
  }

  // FALLBACK: If no valid selection is made
  return <WelcomeView />;
}
