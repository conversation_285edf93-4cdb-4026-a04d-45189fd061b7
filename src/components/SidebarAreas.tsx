import React from "react";

import { Area } from "../mockData";
import ProjectProgressIndicator from "./ProjectProgressIndicator";

export default function SidebarAreas({
  areas,
  selectedKey,
  onSelect,
}: {
  areas: Area[];
  selectedKey: string;
  onSelect: (key: string) => void;
}) {
  return (
    <div className="mt-6">
      {areas.map((area) => (
        <div key={area.name}>
          {(() => {
            const areaKey = `area:${area.name}`;
            const areaSelected = selectedKey === areaKey;
            return (
              <div
                className="flex items-center gap-2 px-4 py-2 rounded cursor-pointer transition-colors group hover:bg-[#363636]"
                onClick={() => onSelect(areaKey)}
              >
                <span className={`text-lg ${areaSelected ? 'text-[var(--sidebar-selected,#f59e42)]' : 'text-gray-100 group-hover:text-white'}`}>🗃️</span>
                <span className={`text-sm flex-1 ${areaSelected ? 'text-[var(--sidebar-selected,#f59e42)] font-semibold' : 'text-gray-100 group-hover:text-white'}`}>{area.name}</span>
                {(() => {
                  const totalTasks = area.projects.reduce((sum, project) => sum + project.tasks.filter(t => !t.completed).length, 0);
                  return totalTasks > 0 ? (
                    <span className={`text-xs px-2 py-1 rounded-full ${areaSelected ? 'bg-orange-600 text-white' : 'bg-gray-600 text-gray-300'}`}>
                      {totalTasks}
                    </span>
                  ) : null;
                })()}
              </div>
            );
          })()}
          <ul className="ml-8 flex flex-col gap-1">
            {area.projects.map((project) => {
              const key = `area:${area.name} ${project.name}`;
              const selected = selectedKey === key;
              return (
                <li
                  key={project.name}
                  className="flex items-center gap-2 px-4 py-2 rounded cursor-pointer transition-colors group hover:bg-[#363636]"
                  onClick={() => onSelect(key)}
                >
                  <ProjectProgressIndicator project={project} size="sm" />
                  <span
                    className={
                      selected
                        ? "text-[var(--sidebar-selected,#f59e42)] font-semibold flex-1"
                        : "text-sm text-gray-100 group-hover:text-white flex-1"
                    }
                    style={selected ? { color: '#f59e42', fontWeight: 600 } : {}}
                  >
                    {project.name}
                  </span>
                  {project.tasks.filter(t => !t.completed).length > 0 && (
                    <span className={`text-xs px-2 py-1 rounded-full ${selected ? 'bg-orange-600 text-white' : 'bg-gray-600 text-gray-300'}`}>
                      {project.tasks.filter(t => !t.completed).length}
                    </span>
                  )}
                </li>
              );
            })}
          </ul>
        </div>
      ))}
    </div>
  );
}
