import React from 'react';
import { useDroppable, useDndContext } from '@dnd-kit/core';
import { Project } from '../mockData';

interface DroppableProjectProps {
  project: Project;
  areaName?: string;
  children: React.ReactNode;
  className?: string;
}

export default function DroppableProject({
  project,
  areaName,
  children,
  className = ''
}: DroppableProjectProps) {
  const { active } = useDndContext();
  const { isOver, setNodeRef } = useDroppable({
    id: `project-drop-${project.id}`,
    data: {
      type: 'project-drop',
      project,
      areaName,
    },
  });

  // Check if we're dragging a task and if it's from a different project
  const isDraggingTask = active?.data?.current?.type === 'task';
  const activeProjectId = active?.data?.current?.projectId;
  const activeAreaName = active?.data?.current?.areaName;

  // Only show drop zone if:
  // 1. We're dragging a task
  // 2. The task is from a different project OR different area
  const shouldShowDropZone = isDraggingTask && (
    activeProjectId !== project.id ||
    activeAreaName !== areaName
  );

  // Only show visual feedback if we should show drop zone and mouse is over
  const showDropFeedback = shouldShowDropZone && isOver;

  return (
    <div
      ref={setNodeRef}
      className={`${className} ${
        shouldShowDropZone
          ? 'relative'
          : ''
      } ${
        showDropFeedback
          ? 'bg-orange-500/20 border-2 border-orange-500 rounded-lg shadow-lg shadow-orange-500/25 transform scale-[1.01]'
          : shouldShowDropZone
          ? 'bg-orange-500/5 border border-orange-500/20 rounded-lg'
          : ''
      } transition-all duration-300`}
    >
      {children}
      {shouldShowDropZone && (
        <div
          className={`absolute inset-0 rounded-lg pointer-events-none transition-all duration-300 ${
            isOver
              ? 'bg-orange-500/25 border-4 border-orange-500 border-dashed opacity-100 shadow-2xl shadow-orange-500/40 dropzone-active'
              : 'bg-orange-500/8 border-2 border-orange-500/30 border-dashed opacity-80'
          }`}
        >
          {isOver && (
            <div className="flex items-center justify-center h-full">
              <div className="bg-gradient-to-r from-orange-500 via-orange-600 to-orange-500 text-white px-8 py-4 rounded-2xl text-lg font-bold shadow-2xl border-2 border-orange-300 transform scale-110 animate-pulse">
                <div className="flex items-center gap-4">
                  <span className="text-2xl animate-bounce">📥</span>
                  <span className="tracking-wide uppercase">Drop Task Here</span>
                  <span className="text-2xl animate-bounce" style={{ animationDelay: '0.2s' }}>📥</span>
                </div>
                {/* Glowing background effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-orange-400 to-orange-600 rounded-2xl opacity-30 blur-sm -z-10"></div>
              </div>
            </div>
          )}
          {/* Corner indicators for extra prominence */}
          {isOver && (
            <>
              <div className="absolute top-2 left-2 w-4 h-4 border-l-4 border-t-4 border-orange-300 rounded-tl-lg"></div>
              <div className="absolute top-2 right-2 w-4 h-4 border-r-4 border-t-4 border-orange-300 rounded-tr-lg"></div>
              <div className="absolute bottom-2 left-2 w-4 h-4 border-l-4 border-b-4 border-orange-300 rounded-bl-lg"></div>
              <div className="absolute bottom-2 right-2 w-4 h-4 border-r-4 border-b-4 border-orange-300 rounded-br-lg"></div>
            </>
          )}
        </div>
      )}
    </div>
  );
}
