import React from 'react';
import { useDroppable, useDndContext } from '@dnd-kit/core';
import { Project } from '../mockData';

interface DroppableProjectProps {
  project: Project;
  areaName?: string;
  children: React.ReactNode;
  className?: string;
}

export default function DroppableProject({
  project,
  areaName,
  children,
  className = ''
}: DroppableProjectProps) {
  const { active } = useDndContext();
  const { isOver, setNodeRef } = useDroppable({
    id: `project-drop-${project.id}`,
    data: {
      type: 'project-drop',
      project,
      areaName,
    },
  });

  // Check if we're dragging a task and if it's from a different project
  const isDraggingTask = active?.data?.current?.type === 'task';
  const activeProjectId = active?.data?.current?.projectId;
  const activeAreaName = active?.data?.current?.areaName;

  // Only show drop zone if:
  // 1. We're dragging a task
  // 2. The task is from a different project OR different area
  const shouldShowDropZone = isDraggingTask && (
    activeProjectId !== project.id ||
    activeAreaName !== areaName
  );

  // Only show visual feedback if we should show drop zone and mouse is over
  const showDropFeedback = shouldShowDropZone && isOver;

  return (
    <div
      ref={setNodeRef}
      className={`${className} ${
        shouldShowDropZone
          ? 'relative'
          : ''
      } ${
        showDropFeedback
          ? 'bg-orange-500/10 border-2 border-orange-500/50 rounded-lg'
          : ''
      } transition-all duration-200`}
    >
      {children}
      {shouldShowDropZone && (
        <div
          className={`absolute inset-0 rounded-lg pointer-events-none transition-all duration-300 ${
            isOver
              ? 'bg-orange-500/15 border-2 border-orange-500 border-dashed opacity-100'
              : 'bg-orange-500/5 border-2 border-orange-500/20 border-dashed opacity-70'
          }`}
        >
          {isOver && (
            <div className="flex items-center justify-center h-full">
              <div className="bg-orange-500 text-white px-4 py-2 rounded-lg text-sm font-medium shadow-xl border border-orange-400">
                <div className="flex items-center gap-2">
                  <span>📥</span>
                  <span>Drop task here</span>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
