import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { Project } from '../mockData';

interface DroppableProjectProps {
  project: Project;
  areaName?: string;
  children: React.ReactNode;
  className?: string;
}

export default function DroppableProject({ 
  project, 
  areaName, 
  children, 
  className = '' 
}: DroppableProjectProps) {
  const { isOver, setNodeRef } = useDroppable({
    id: `project-drop-${project.id}`,
    data: {
      type: 'project-drop',
      project,
      areaName,
    },
  });

  return (
    <div
      ref={setNodeRef}
      className={`${className} ${
        isOver 
          ? 'bg-orange-500/10 border-2 border-orange-500/50 rounded-lg' 
          : ''
      } transition-all duration-200`}
    >
      {children}
      {isOver && (
        <div className="absolute inset-0 bg-orange-500/5 border-2 border-orange-500 border-dashed rounded-lg pointer-events-none flex items-center justify-center">
          <div className="bg-orange-500 text-white px-3 py-1 rounded text-sm font-medium">
            Drop task here
          </div>
        </div>
      )}
    </div>
  );
}
