import React from 'react';
import { Task, Project } from '../../mockData';
import TaskItem from '../TaskItem';
import { isTodayTask } from '../../utils/date';

interface TaskGroupProps {
  title: string;
  tasks: (Task & { project?: Project; areaName?: string })[];
  showTodayStar?: boolean;
  showInLogbook?: boolean;
  onToggleChecklist: (
    projectId: string,
    taskId: string,
    checklistId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onToggleTask: (
    projectId: string,
    taskId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
}

export default function TaskGroup({
  title,
  tasks,
  showTodayStar = false,
  showInLogbook = false,
  onToggleChecklist,
  onToggleTask
}: TaskGroupProps) {
  if (tasks.length === 0) return null;

  return (
    <div className="mb-8">
      <div className="font-semibold text-lg text-orange-400 mb-2">
        {title}
      </div>
      <ul className="space-y-4">
        {tasks.map(task => (
          <TaskItem
            key={task.id}
            task={task}
            project={task.project}
            areaName={task.areaName}
            showTodayStar={showTodayStar && isTodayTask(task)}
            showInLogbook={showInLogbook}
            onToggleChecklist={onToggleChecklist}
            onToggleTask={onToggleTask}
          />
        ))}
      </ul>
    </div>
  );
}
