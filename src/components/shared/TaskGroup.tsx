import React from 'react';
import { Task, Project } from '../../mockData';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import DraggableTaskItem from '../DraggableTaskItem';
import { isTodayTask } from '../../utils/date';

interface TaskGroupProps {
  title: string;
  tasks: (Task & { project?: Project; areaName?: string })[];
  showTodayStar?: boolean;
  showInLogbook?: boolean;
  onToggleChecklist: (
    projectId: string,
    taskId: string,
    checklistId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onToggleTask: (
    projectId: string,
    taskId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onEditTask?: (task: Task, projectId: string, areaName?: string) => void;
}

export default function TaskGroup({
  title,
  tasks,
  showTodayStar = false,
  showInLogbook = false,
  onToggleChecklist,
  onToggleTask,
  onEditTask
}: TaskGroupProps) {
  if (tasks.length === 0) return null;

  const taskIds = tasks.map(task => task.id);

  const taskList = tasks.map(task => (
    <DraggableTaskItem
      key={task.id}
      task={task}
      project={task.project}
      areaName={task.areaName}
      showTodayStar={showTodayStar && isTodayTask(task)}
      showInLogbook={showInLogbook}
      onToggleChecklist={onToggleChecklist}
      onToggleTask={onToggleTask}
      onEditTask={onEditTask}
    />
  ));

  return (
    <div className="mb-8">
      <div className="font-semibold text-lg text-orange-400 mb-2">
        {title}
      </div>
      <SortableContext items={taskIds} strategy={verticalListSortingStrategy}>
        <ul className="space-y-4">
          {taskList}
        </ul>
      </SortableContext>
    </div>
  );
}
