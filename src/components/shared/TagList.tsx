import React from 'react';

interface TagListProps {
  tags: string[];
  className?: string;
}

export default function TagList({ tags, className = "mb-6" }: TagListProps) {
  if (!tags || tags.length === 0) return null;

  return (
    <div className={className}>
      {tags.map(tag => (
        <span key={tag} className="inline-block bg-blue-600 text-white px-2 py-1 rounded text-sm mr-2">
          {tag}
        </span>
      ))}
    </div>
  );
}
