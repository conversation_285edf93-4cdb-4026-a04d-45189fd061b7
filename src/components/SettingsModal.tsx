import React from "react";
import Modal from "./Modal";

export default function SettingsModal({ open, onClose }:{ open:boolean, onClose:()=>void }) {
  return (
    <Modal open={open} onClose={onClose}>
      <div className="bg-white dark:bg-gray-900 p-6 rounded shadow-lg w-80 relative">
        <div className="text-lg font-bold mb-4 text-gray-900 dark:text-gray-100">Settings</div>
        <button className="absolute top-2 right-2 text-gray-400 hover:text-gray-900 dark:hover:text-gray-100" onClick={onClose}>×</button>
        <div className="text-gray-700 dark:text-gray-300">Settings content goes here.</div>
      </div>
    </Modal>
  );
}
