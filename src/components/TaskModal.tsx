import React, { useState, useEffect } from 'react';
import { Task, ChecklistItem } from '../mockData';
import Modal from './Modal';

interface TaskModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (task: Omit<Task, 'id'>) => void;
  task?: Task; // For editing existing task
  projectId?: string;
  areaId?: string;
}

export default function TaskModal({ 
  open, 
  onClose, 
  onSave, 
  task, 
  projectId, 
  areaId 
}: TaskModalProps) {
  const [title, setTitle] = useState('');
  const [notes, setNotes] = useState('');
  const [dueDate, setDueDate] = useState('');
  const [startDate, setStartDate] = useState('');
  const [thisEvening, setThisEvening] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [checklist, setChecklist] = useState<ChecklistItem[]>([]);
  const [checklistInput, setChecklistInput] = useState('');

  // Reset form when modal opens/closes or task changes
  useEffect(() => {
    if (open) {
      if (task) {
        // Editing existing task
        setTitle(task.title);
        setNotes(task.notes || '');
        setDueDate(task.dueDate || '');
        setStartDate(task.startDate || '');
        setThisEvening(task.thisEvening || false);
        setTags(task.tags || []);
        setChecklist(task.checklist || []);
      } else {
        // Creating new task
        setTitle('');
        setNotes('');
        setDueDate('');
        setStartDate('');
        setThisEvening(false);
        setTags([]);
        setChecklist([]);
      }
      setTagInput('');
      setChecklistInput('');
    }
  }, [open, task]);

  const handleSave = () => {
    if (!title.trim()) return;

    const taskData: Omit<Task, 'id'> = {
      title: title.trim(),
      notes: notes.trim() || undefined,
      dueDate: dueDate || undefined,
      startDate: startDate || undefined,
      thisEvening: thisEvening || undefined,
      tags: tags.length > 0 ? tags : undefined,
      checklist: checklist.length > 0 ? checklist : undefined,
      completed: task?.completed || false,
      projectId,
      areaId,
    };

    onSave(taskData);
    onClose();
  };

  const handleAddTag = () => {
    const tag = tagInput.trim().toLowerCase();
    if (tag && !tags.includes(tag)) {
      setTags([...tags, tag]);
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleAddChecklistItem = () => {
    const text = checklistInput.trim();
    if (text) {
      const newItem: ChecklistItem = {
        id: `c_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        text,
        completed: false,
      };
      setChecklist([...checklist, newItem]);
      setChecklistInput('');
    }
  };

  const handleRemoveChecklistItem = (itemId: string) => {
    setChecklist(checklist.filter(item => item.id !== itemId));
  };

  const handleToggleChecklistItem = (itemId: string) => {
    setChecklist(checklist.map(item =>
      item.id === itemId ? { ...item, completed: !item.completed } : item
    ));
  };

  return (
    <Modal open={open} onClose={onClose}>
      <div className="bg-[#2a2a2a] rounded-lg p-6 w-full max-w-md mx-auto">
        <h2 className="text-xl font-bold text-white mb-4">
          {task ? 'Edit Task' : 'New Task'}
        </h2>

        {/* Title */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Title *
          </label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full px-3 py-2 bg-[#1a1a1a] border border-gray-600 rounded text-white focus:outline-none focus:border-orange-500"
            placeholder="Enter task title..."
            autoFocus
          />
        </div>

        {/* Notes */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Notes
          </label>
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            className="w-full px-3 py-2 bg-[#1a1a1a] border border-gray-600 rounded text-white focus:outline-none focus:border-orange-500 resize-none"
            rows={3}
            placeholder="Add notes..."
          />
        </div>

        {/* Dates */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Start Date
            </label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-full px-3 py-2 bg-[#1a1a1a] border border-gray-600 rounded text-white focus:outline-none focus:border-orange-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Due Date
            </label>
            <input
              type="date"
              value={dueDate}
              onChange={(e) => setDueDate(e.target.value)}
              className="w-full px-3 py-2 bg-[#1a1a1a] border border-gray-600 rounded text-white focus:outline-none focus:border-orange-500"
            />
          </div>
        </div>

        {/* This Evening */}
        <div className="mb-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={thisEvening}
              onChange={(e) => setThisEvening(e.target.checked)}
              className="mr-2"
            />
            <span className="text-sm text-gray-300">This Evening</span>
          </label>
        </div>

        {/* Tags */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Tags
          </label>
          <div className="flex gap-2 mb-2">
            <input
              type="text"
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
              className="flex-1 px-3 py-2 bg-[#1a1a1a] border border-gray-600 rounded text-white focus:outline-none focus:border-orange-500"
              placeholder="Add tag..."
            />
            <button
              onClick={handleAddTag}
              className="px-3 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
            >
              Add
            </button>
          </div>
          <div className="flex flex-wrap gap-2">
            {tags.map(tag => (
              <span
                key={tag}
                className="inline-flex items-center px-2 py-1 bg-gray-700 text-xs rounded"
              >
                #{tag}
                <button
                  onClick={() => handleRemoveTag(tag)}
                  className="ml-1 text-gray-400 hover:text-white"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
        </div>

        {/* Checklist */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Checklist
          </label>
          <div className="flex gap-2 mb-2">
            <input
              type="text"
              value={checklistInput}
              onChange={(e) => setChecklistInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleAddChecklistItem()}
              className="flex-1 px-3 py-2 bg-[#1a1a1a] border border-gray-600 rounded text-white focus:outline-none focus:border-orange-500"
              placeholder="Add checklist item..."
            />
            <button
              onClick={handleAddChecklistItem}
              className="px-3 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
            >
              Add
            </button>
          </div>
          <div className="space-y-1">
            {checklist.map(item => (
              <div key={item.id} className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={item.completed}
                  onChange={() => handleToggleChecklistItem(item.id)}
                  className="rounded"
                />
                <span className={`flex-1 text-sm ${item.completed ? 'line-through text-gray-500' : 'text-gray-300'}`}>
                  {item.text}
                </span>
                <button
                  onClick={() => handleRemoveChecklistItem(item.id)}
                  className="text-gray-400 hover:text-white"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-300 hover:text-white"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={!title.trim()}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {task ? 'Save Changes' : 'Create Task'}
          </button>
        </div>
      </div>
    </Modal>
  );
}
