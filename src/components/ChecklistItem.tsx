import React from "react";

interface ChecklistItemProps {
  item: { id: string; completed: boolean; text: string };
  projectId: string;
  taskId: string;
  areaName?: string;
  onToggle: (
    projectId: string,
    taskId: string,
    checklistId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
}

const ChecklistItem: React.FC<ChecklistItemProps> = ({ item, projectId, taskId, areaName, onToggle }) => {
  return (
    <li className={`flex items-center gap-2 ${item.completed ? 'line-through text-gray-400' : 'text-white'}`}>
      <button
        type="button"
        role="checkbox"
        aria-checked={item.completed}
        tabIndex={0}
        className={`mr-3 flex items-center justify-center rounded-full border-2 transition-all duration-150 text-sm ${item.completed ? 'bg-[var(--sidebar-selected,#f59e42)] border-[var(--sidebar-selected,#f59e42)]' : 'bg-transparent border-gray-400 hover:border-white'}`}
        style={{ width: '1.1em', height: '1.1em', minWidth: '1.1em', minHeight: '1.1em', lineHeight: 1, padding: 0 }}
        onClick={() => {
          const inArea = !!areaName;
          onToggle(projectId, taskId, item.id, inArea, areaName ?? "");
        }}
      >
        {item.completed ? (
          <svg
            className="text-white"
            fill="none"
            stroke="currentColor"
            strokeWidth="2.5"
            viewBox="0 0 20 20"
            style={{ width: '1em', height: '1em', display: 'block' }}
          >
            <polyline points="5 11 9 15 15 7" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        ) : null}
      </button>
      <span className="align-middle select-none">{item.text}</span>
    </li>
  );
};

export default ChecklistItem;
