import React from "react";

import { Project } from "../mockData";
import ProjectProgressIndicator from "./ProjectProgressIndicator";

export default function SidebarProjects({projects, selectedKey, onSelect}: {projects: Project[], selectedKey:string, onSelect:(key:string)=>void}) {
  const projectsViewSelected = selectedKey === 'view:projects';

  return (
    <div>
      {/* Projects header */}
      <div
        className={`flex items-center gap-2 px-4 py-2 rounded cursor-pointer transition-colors group hover:bg-[#363636] ${
          projectsViewSelected ? 'bg-[#363636]' : ''
        }`}
        onClick={() => onSelect('view:projects')}
      >
        <span className={`text-lg ${projectsViewSelected ? 'text-[var(--sidebar-selected,#f59e42)]' : 'text-gray-100 group-hover:text-white'}`}>📁</span>
        <span className={`text-sm font-medium ${projectsViewSelected ? 'text-[var(--sidebar-selected,#f59e42)]' : 'text-gray-100 group-hover:text-white'}`}>
          Projects
        </span>
        {projects.length > 0 && (
          <span className={`text-xs px-2 py-1 rounded-full ml-auto ${projectsViewSelected ? 'bg-orange-600 text-white' : 'bg-gray-600 text-gray-300'}`}>
            {projects.length}
          </span>
        )}
      </div>

      <ul className="flex flex-col gap-1 mt-2 ml-4">
        {projects.map((project) => {
        const key = `project:${project.name}`;
        const selected = selectedKey === key;
        return (
          <li
            key={project.name}
            className={`flex items-center gap-2 px-4 py-2 rounded cursor-pointer transition-colors group hover:bg-[#363636]`}
            onClick={() => onSelect(key)}
          >
            <ProjectProgressIndicator project={project} size="sm" />
            <span
              className={
                selected
                  ? "text-[var(--sidebar-selected,#f59e42)] font-semibold flex-1"
                  : "text-sm text-gray-100 group-hover:text-white flex-1"
              }
              style={selected ? { color: '#f59e42', fontWeight: 600 } : {}}
            >
              {project.name}
            </span>
            {project.tasks.filter(t => !t.completed).length > 0 && (
              <span className={`text-xs px-2 py-1 rounded-full ${selected ? 'bg-orange-600 text-white' : 'bg-gray-600 text-gray-300'}`}>
                {project.tasks.filter(t => !t.completed).length}
              </span>
            )}
          </li>
        );
      })}
      </ul>
    </div>
  );
}
