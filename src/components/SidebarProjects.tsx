import React from "react";

import { Project } from "../mockData";

export default function SidebarProjects({projects, selectedKey, onSelect}: {projects: Project[], selectedKey:string, onSelect:(key:string)=>void}) {
  return (
    <ul className="flex flex-col gap-1 mt-6">
      {projects.map((project) => {
        const key = `project:${project.name}`;
        const selected = selectedKey === key;
        return (
          <li
            key={project.name}
            className={`flex items-center gap-2 px-4 py-2 rounded cursor-pointer transition-colors group hover:bg-[#363636]`}
            onClick={() => onSelect(key)}
          >
            <span className={`w-4 h-4 border rounded-full inline-block ${selected ? 'border-[var(--sidebar-selected,#f59e42)]' : 'border-gray-600'} group-hover:border-white`} />
            <span
              className={
                selected
                  ? "text-[var(--sidebar-selected,#f59e42)] font-semibold flex-1"
                  : "text-sm text-gray-100 group-hover:text-white flex-1"
              }
              style={selected ? { color: '#f59e42', fontWeight: 600 } : {}}
            >
              {project.name}
            </span>
            {project.tasks.filter(t => !t.completed).length > 0 && (
              <span className={`text-xs px-2 py-1 rounded-full ${selected ? 'bg-orange-600 text-white' : 'bg-gray-600 text-gray-300'}`}>
                {project.tasks.filter(t => !t.completed).length}
              </span>
            )}
          </li>
        );
      })}
    </ul>
  );
}
