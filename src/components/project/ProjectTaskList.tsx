import React from 'react';
import { Task, Project } from '../../mockData';
import TaskItem from '../TaskItem';
import EmptyState from '../shared/EmptyState';

interface ProjectTaskListProps {
  project: Project;
  areaName?: string;
  onToggleChecklist: (
    projectId: string,
    taskId: string,
    checklistId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onToggleTask: (
    projectId: string,
    taskId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
}

export default function ProjectTaskList({ 
  project, 
  areaName, 
  onToggleChecklist, 
  onToggleTask 
}: ProjectTaskListProps) {
  if (project.tasks.length === 0) {
    return <EmptyState message="No tasks in this project yet." />;
  }

  return (
    <ul className="space-y-4">
      {project.tasks.map(task => (
        <TaskItem
          key={task.id}
          task={task}
          project={project}
          areaName={areaName}
          onToggleChecklist={onToggleChecklist}
          onToggleTask={onToggleTask}
        />
      ))}
    </ul>
  );
}
