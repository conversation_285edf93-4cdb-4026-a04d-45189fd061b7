import React from 'react';
import { Project } from '../../mockData';
import TagList from '../shared/TagList';

interface ProjectHeaderProps {
  project: Project;
  areaName?: string;
  className?: string;
}

export default function ProjectHeader({ project, areaName, className = "mb-6" }: ProjectHeaderProps) {
  return (
    <div className={className}>
      {areaName && (
        <div className="mb-2 text-orange-400 text-sm">{areaName}</div>
      )}
      <h2 className="text-2xl font-bold mb-4 text-white">{project.name}</h2>
      
      {project.notes && (
        <div className="mb-6 text-gray-300 bg-[#232323] p-4 rounded">
          {project.notes}
        </div>
      )}
      
      {project.deadline && (
        <div className="mb-6 text-orange-400">
          <strong>Deadline:</strong> {project.deadline}
        </div>
      )}
      
      <TagList tags={project.tags || []} />
    </div>
  );
}
