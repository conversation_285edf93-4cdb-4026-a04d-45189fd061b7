import React from 'react';
import { Project } from '../../mockData';
import TagList from '../shared/TagList';
import ProjectProgressIndicator from '../ProjectProgressIndicator';

interface ProjectHeaderProps {
  project: Project;
  areaName?: string;
  className?: string;
  onEditProject?: (project: Project, areaName?: string) => void;
}

export default function ProjectHeader({ project, areaName, className = "mb-6", onEditProject }: ProjectHeaderProps) {
  return (
    <div className={className}>
      {areaName && (
        <div className="mb-2 text-orange-400 text-sm">{areaName}</div>
      )}
      <div className="flex items-center gap-3 mb-4">
        <ProjectProgressIndicator project={project} size="lg" />
        <h2 className="text-2xl font-bold text-white flex-1">{project.name}</h2>
        {onEditProject && (
          <button
            onClick={() => onEditProject(project, areaName)}
            className="px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600 hover:text-white transition-colors"
          >
            Edit
          </button>
        )}
      </div>
      
      {project.notes && (
        <div className="mb-6 text-gray-300 bg-[#232323] p-4 rounded">
          {project.notes}
        </div>
      )}
      
      {project.deadline && (
        <div className="mb-6 text-orange-400">
          <strong>Deadline:</strong> {project.deadline}
        </div>
      )}
      
      <TagList tags={project.tags || []} />
    </div>
  );
}
