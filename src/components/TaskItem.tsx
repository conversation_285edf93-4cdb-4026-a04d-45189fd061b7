import React from "react";
import { Project, Task } from "../mockData";
import ChecklistItem from "./ChecklistItem";
import TaskCheckbox from "./TaskCheckbox";

interface TaskItemProps {
  task: Task;
  project?: Project;
  areaName?: string;
  showTodayStar?: boolean; // For showing yellow star in Anytime filter
  onToggleChecklist: (
    projectId: string,
    taskId: string,
    checklistId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onToggleTask: (
    projectId: string,
    taskId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
}

const TaskItem: React.FC<TaskItemProps> = ({ task, project, areaName, showTodayStar, onToggleChecklist, onToggleTask }) => {
  return (
    <li className="rounded p-4 bg-[#232323]">
      <div className="flex items-center gap-2">
        <TaskCheckbox
          checked={task.completed}
          onClick={() => {
            const inArea = !!areaName;
            console.log('TaskCheckbox clicked:', {
              projectId: project?.id,
              taskId: task.id,
              inArea,
              areaName
            });
            onToggleTask(project?.id ?? '', task.id, inArea, areaName);
          }}
          ariaLabel={`Mark task '${task.title}' as completed`}
        />
        {showTodayStar && (
          <span className="text-lg" style={{ color: '#fff700' }}>★</span>
        )}
        <span className={task.completed ? 'line-through text-gray-400' : 'text-white font-semibold'}>
          <strong>{task.title}</strong>
        </span>
        {task.completed && <span className="text-green-400">&#10003;</span>}
      </div>
      {task.notes && <div className="text-sm text-gray-300 mt-1">{task.notes}</div>}
      {task.dueDate && <div className="text-xs text-gray-400 mt-1">Due: {task.dueDate}</div>}
      {task.tags && (
        <div className="mt-1">
          {task.tags.map((tag: string) => (
            <span key={tag} className="inline-block bg-gray-700 text-xs px-2 py-1 rounded mr-2">#{tag}</span>
          ))}
        </div>
      )}
      {task.checklist && task.checklist.length > 0 && (
        <ul className="mt-2 ml-4 text-sm">
          {task.checklist.map((item) => (
            <ChecklistItem
              key={item.id}
              item={item}
              projectId={project?.id ?? ''}
              taskId={task.id}
              areaName={areaName}
              onToggle={onToggleChecklist}
            />
          ))}
        </ul>
      )}
    </li>
  );
};

export default TaskItem;
