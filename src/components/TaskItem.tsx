import React, { useState, useEffect } from "react";
import { Project, Task } from "../mockData";
import ChecklistItem from "./ChecklistItem";
import TaskCheckbox from "./TaskCheckbox";

interface TaskItemProps {
  task: Task;
  project?: Project;
  areaName?: string;
  showTodayStar?: boolean; // For showing yellow star in Anytime filter
  showInLogbook?: boolean; // For showing completed tasks in Logbook
  onToggleChecklist: (
    projectId: string,
    taskId: string,
    checklistId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onToggleTask: (
    projectId: string,
    taskId: string,
    inArea: boolean,
    areaName?: string
  ) => void;
  onEditTask?: (task: Task, projectId: string, areaName?: string) => void;
}

const TaskItem: React.FC<TaskItemProps> = ({ task, project, areaName, showTodayStar, showInLogbook, onToggleChecklist, onToggleTask, onEditTask }) => {
  const [isCompleting, setIsCompleting] = useState(false);
  const [shouldHide, setShouldHide] = useState(false);

  const handleTaskToggle = () => {
    if (!task.completed) {
      // Task is being completed
      setIsCompleting(true);

      // After animation delay, actually toggle the task
      setTimeout(() => {
        const inArea = !!areaName;
        onToggleTask(project?.id ?? '', task.id, inArea, areaName);

        // Start hide animation
        setShouldHide(true);
      }, 1500); // 1.5 second delay before hiding
    } else {
      // Task is being uncompleted - immediate toggle
      const inArea = !!areaName;
      onToggleTask(project?.id ?? '', task.id, inArea, areaName);
    }
  };

  // Reset states when task completion status changes externally
  useEffect(() => {
    if (!task.completed) {
      setIsCompleting(false);
      setShouldHide(false);
    }
  }, [task.completed]);

  if (shouldHide && task.completed && !showInLogbook) {
    return null; // Hide completed tasks after animation (except in Logbook)
  }

  return (
    <li className={`group rounded p-4 bg-[#232323] transition-all duration-500 ${
      isCompleting ? 'opacity-50 transform scale-95' : 'opacity-100 transform scale-100'
    } ${shouldHide ? 'opacity-0 transform scale-95' : ''}`}>
      <div className="flex items-center gap-2">
        <TaskCheckbox
          checked={task.completed || isCompleting}
          onClick={handleTaskToggle}
          ariaLabel={`Mark task '${task.title}' as completed`}
        />
        {showTodayStar && (
          <span className="text-lg" style={{ color: '#fff700' }}>★</span>
        )}
        <div className="flex-1">
          <div className={`text-white font-medium transition-all duration-300 ${
            isCompleting ? 'line-through text-gray-500' : ''
          } ${task.completed ? 'line-through text-gray-400' : ''}`}>
            <strong>{task.title}</strong>
          </div>
          {task.notes && (
            <div className={`text-sm text-gray-300 mt-1 transition-all duration-300 ${
              isCompleting ? 'line-through text-gray-600' : ''
            } ${task.completed ? 'line-through text-gray-500' : ''}`}>
              {task.notes}
            </div>
          )}
          {task.dueDate && (
            <div className={`text-xs text-gray-400 mt-1 transition-all duration-300 ${
              isCompleting ? 'line-through text-gray-600' : ''
            } ${task.completed ? 'line-through text-gray-500' : ''}`}>
              Due: {task.dueDate}
            </div>
          )}
        </div>
        <div className="flex items-center gap-2">
          {onEditTask && (
            <button
              onClick={() => onEditTask(task, project?.id || '', areaName)}
              className="px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded hover:bg-gray-600 hover:text-white transition-colors opacity-0 group-hover:opacity-100"
            >
              Edit
            </button>
          )}
          {task.completed && <span className="text-green-400">&#10003;</span>}
        </div>
      </div>
      {task.tags && (
        <div className="mt-1">
          {task.tags.map((tag: string) => (
            <span key={tag} className="inline-block bg-gray-700 text-xs px-2 py-1 rounded mr-2">#{tag}</span>
          ))}
        </div>
      )}
      {task.checklist && task.checklist.length > 0 && (
        <ul className="mt-2 ml-4 text-sm">
          {task.checklist.map((item) => (
            <ChecklistItem
              key={item.id}
              item={item}
              projectId={project?.id ?? ''}
              taskId={task.id}
              areaName={areaName}
              onToggle={onToggleChecklist}
            />
          ))}
        </ul>
      )}
    </li>
  );
};

export default TaskItem;
