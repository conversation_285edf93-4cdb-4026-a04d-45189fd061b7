import React, { useState, useEffect } from 'react';
import { Project } from '../mockData';
import Modal from './Modal';

interface ProjectModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (project: Omit<Project, 'id' | 'tasks'>) => void;
  project?: Project; // For editing existing project
  areaId?: string;
}

export default function ProjectModal({ 
  open, 
  onClose, 
  onSave, 
  project,
  areaId 
}: ProjectModalProps) {
  const [name, setName] = useState('');
  const [notes, setNotes] = useState('');
  const [deadline, setDeadline] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');

  // Reset form when modal opens/closes or project changes
  useEffect(() => {
    if (open) {
      if (project) {
        // Editing existing project
        setName(project.name);
        setNotes(project.notes || '');
        setDeadline(project.deadline || '');
        setTags(project.tags || []);
      } else {
        // Creating new project
        setName('');
        setNotes('');
        setDeadline('');
        setTags([]);
      }
      setTagInput('');
    }
  }, [open, project]);

  const handleSave = () => {
    if (!name.trim()) return;

    const projectData: Omit<Project, 'id' | 'tasks'> = {
      name: name.trim(),
      notes: notes.trim() || undefined,
      deadline: deadline || undefined,
      tags: tags.length > 0 ? tags : undefined,
    };

    onSave(projectData);
    onClose();
  };

  const handleAddTag = () => {
    const tag = tagInput.trim().toLowerCase();
    if (tag && !tags.includes(tag)) {
      setTags([...tags, tag]);
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.target === e.currentTarget) {
      handleSave();
    }
  };

  return (
    <Modal open={open} onClose={onClose}>
      <div className="bg-[#2a2a2a] rounded-lg p-6 w-full max-w-md mx-auto">
        <h2 className="text-xl font-bold text-white mb-4">
          {project ? 'Edit Project' : 'New Project'}
        </h2>

        {/* Name */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Project Name *
          </label>
          <input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            onKeyPress={handleKeyPress}
            className="w-full px-3 py-2 bg-[#1a1a1a] border border-gray-600 rounded text-white focus:outline-none focus:border-orange-500"
            placeholder="Enter project name..."
            autoFocus
          />
        </div>

        {/* Notes */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Notes
          </label>
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            className="w-full px-3 py-2 bg-[#1a1a1a] border border-gray-600 rounded text-white focus:outline-none focus:border-orange-500 resize-none"
            rows={3}
            placeholder="Add project notes..."
          />
        </div>

        {/* Deadline */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Deadline
          </label>
          <input
            type="date"
            value={deadline}
            onChange={(e) => setDeadline(e.target.value)}
            className="w-full px-3 py-2 bg-[#1a1a1a] border border-gray-600 rounded text-white focus:outline-none focus:border-orange-500"
          />
        </div>

        {/* Tags */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Tags
          </label>
          <div className="flex gap-2 mb-2">
            <input
              type="text"
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
              className="flex-1 px-3 py-2 bg-[#1a1a1a] border border-gray-600 rounded text-white focus:outline-none focus:border-orange-500"
              placeholder="Add tag..."
            />
            <button
              onClick={handleAddTag}
              className="px-3 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
            >
              Add
            </button>
          </div>
          <div className="flex flex-wrap gap-2">
            {tags.map(tag => (
              <span
                key={tag}
                className="inline-flex items-center px-2 py-1 bg-gray-700 text-xs rounded"
              >
                #{tag}
                <button
                  onClick={() => handleRemoveTag(tag)}
                  className="ml-1 text-gray-400 hover:text-white"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
        </div>

        {/* Area Info */}
        {areaId && (
          <div className="mb-4 p-3 bg-[#1a1a1a] rounded">
            <div className="text-sm text-gray-400">
              This project will be created in the selected area.
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-300 hover:text-white"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={!name.trim()}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {project ? 'Save Changes' : 'Create Project'}
          </button>
        </div>
      </div>
    </Modal>
  );
}
