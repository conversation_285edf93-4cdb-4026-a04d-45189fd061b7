'use client';
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import MainContent from "@/components/MainContent";
import React, { useState, useCallback } from "react";
import { areas as mockAreas, projects as mockProjects, Area, Project, Task } from "../mockData";
import { calculateFilterCounts } from "../utils/filterDefinitions";
import { useTaskActions } from "../hooks/useTaskActions";
import { useDragDrop } from "../hooks/useDragDrop";
import TaskModal from "../components/TaskModal";
import ProjectModal from "../components/ProjectModal";
import AreaModal from "../components/AreaModal";
import { DndContext, DragEndEvent, DragOverEvent, closestCenter, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove } from '@dnd-kit/sortable';


export default function Home() {
  // Move sidebar state up so it can be shared
  const [selectedKey, setSelectedKey] = useState<string>("filter:Today");
  const [areas, setAreas] = useState<Area[]>(mockAreas);
  const [projects, setProjects] = useState<Project[]>(mockProjects);

  // Modal states
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [showProjectModal, setShowProjectModal] = useState(false);
  const [showAreaModal, setShowAreaModal] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | undefined>();
  const [editingProject, setEditingProject] = useState<Project | undefined>();
  const [editingArea, setEditingArea] = useState<Area | undefined>();
  const [modalContext, setModalContext] = useState<{
    projectId?: string;
    areaId?: string;
    areaName?: string;
  }>({});

  // Helper to generate a unique id
  const generateId = (prefix: string = "id"): string => `${prefix}_${Math.random().toString(36).substr(2, 9)}`;

  // Add new area and select it
  const handleAddArea = () => {
    const newAreaName = `New Area`;
    const newArea: Area = {
      id: generateId('area'),
      name: newAreaName,
      projects: [],
    };
    setAreas(prev => [...prev, newArea]);
    setSelectedKey(`area:${newAreaName}`);
  };

  // Update area name
  const handleAreaNameChange = (areaKey: string, newName: string) => {
    setAreas(prev => prev.map(area =>
      `area:${area.name}` === areaKey ? { ...area, name: newName } : area
    ));
    // If the selectedKey was the old name, update it
    if (selectedKey === areaKey) {
      setSelectedKey(`area:${newName}`);
    }
  };

  // Add new project and select it
  const handleAddProject = () => {
    // Helper to generate a unique project name
    const getUniqueProjectName = (baseName: string, existingNames: string[]) => {
      if (!existingNames.includes(baseName)) return baseName;
      let i = 2;
      let newName = `${baseName} ${i}`;
      while (existingNames.includes(newName)) {
        i++;
        newName = `${baseName} ${i}`;
      }
      return newName;
    };

    // 1. Area or area project selected
    if (selectedKey.startsWith("area:")) {
      const areaProjectKey = selectedKey.slice(5).trim();
      let matchedArea: Area | undefined = undefined;
      let matchedAreaName = '';
      for (const area of areas) {
        if (areaProjectKey.startsWith(area.name)) {
          if (!matchedArea || area.name.length > matchedAreaName.length) {
            matchedArea = area;
            matchedAreaName = area.name;
          }
        }
      }
      if (matchedArea) {
        const existingNames = matchedArea.projects.map(p => p.name);
        const uniqueName = getUniqueProjectName("New Project", existingNames);
        const newProject: Project = {
          id: generateId('project'),
          name: uniqueName,
          tasks: [],
        };
        setAreas(prev => prev.map(area =>
          area.name === matchedAreaName
            ? { ...area, projects: [...area.projects, newProject] }
            : area
        ));
        setSelectedKey(`area:${matchedAreaName} ${uniqueName}`);
        return;
      }
    }
    // 2. Otherwise, add to standalone projects
    const existingNames = projects.map(p => p.name);
    const uniqueName = getUniqueProjectName("New Project", existingNames);
    const newProject: Project = {
      id: generateId('project'),
      name: uniqueName,
      tasks: [],
    };
    setProjects(prev => [...prev, newProject]);
    setSelectedKey(`project:${uniqueName}`);
  };

  // Use the task actions hook
  const { handleToggleTask, handleToggleChecklist, handleAddTask, handleUpdateTask } = useTaskActions({
    setAreas,
    setProjects
  });

  // Use the drag drop hook
  const { moveTask, reorderTasks, reorderProjects, reorderStandaloneProjects } = useDragDrop({
    areas,
    projects,
    setAreas,
    setProjects
  });

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Modal handlers
  const handleSaveTask = useCallback((taskData: Omit<Task, 'id'>) => {
    if (editingTask) {
      // Update existing task
      handleUpdateTask(
        editingTask.id,
        taskData,
        modalContext.projectId || '',
        !!modalContext.areaName,
        modalContext.areaName
      );
    } else {
      // Add new task
      handleAddTask(
        taskData,
        modalContext.projectId || '',
        !!modalContext.areaName,
        modalContext.areaName
      );
    }
    setEditingTask(undefined);
    setModalContext({});
  }, [editingTask, modalContext, handleAddTask, handleUpdateTask]);

  const handleSaveProject = useCallback((projectData: Omit<Project, 'id' | 'tasks'>) => {
    if (editingProject) {
      // Update existing project
      if (modalContext.areaName) {
        setAreas(prev => prev.map(area =>
          area.name === modalContext.areaName
            ? {
                ...area,
                projects: area.projects.map(project =>
                  project.id === editingProject.id
                    ? { ...project, ...projectData }
                    : project
                )
              }
            : area
        ));
      } else {
        setProjects(prev => prev.map(project =>
          project.id === editingProject.id
            ? { ...project, ...projectData }
            : project
        ));
      }
    } else {
      // Add new project
      const newProject: Project = {
        ...projectData,
        id: generateId('project'),
        tasks: [],
      };

      if (modalContext.areaName) {
        setAreas(prev => prev.map(area =>
          area.name === modalContext.areaName
            ? { ...area, projects: [...area.projects, newProject] }
            : area
        ));
        setSelectedKey(`area:${modalContext.areaName} ${newProject.name}`);
      } else {
        setProjects(prev => [...prev, newProject]);
        setSelectedKey(`project:${newProject.name}`);
      }
    }
    setEditingProject(undefined);
    setModalContext({});
  }, [editingProject, modalContext, generateId, setAreas, setProjects, setSelectedKey]);

  const handleSaveArea = useCallback((areaData: Omit<Area, 'id' | 'projects'>) => {
    if (editingArea) {
      // Update existing area
      const oldName = editingArea.name;
      setAreas(prev => prev.map(area =>
        area.id === editingArea.id
          ? { ...area, ...areaData }
          : area
      ));
      // Update selected key if it was pointing to this area
      if (selectedKey.startsWith(`area:${oldName}`)) {
        const suffix = selectedKey.slice(`area:${oldName}`.length);
        setSelectedKey(`area:${areaData.name}${suffix}`);
      }
    } else {
      // Add new area
      const newArea: Area = {
        ...areaData,
        id: generateId('area'),
        projects: [],
      };
      setAreas(prev => [...prev, newArea]);
      setSelectedKey(`area:${newArea.name}`);
    }
    setEditingArea(undefined);
  }, [editingArea, selectedKey, generateId, setAreas, setSelectedKey]);

  // Handle drag end
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) return;

    const activeData = active.data.current;
    const overData = over.data.current;

    if (activeData?.type === 'task') {
      const activeTask = activeData.task;
      const activeProjectId = activeData.projectId;
      const activeAreaName = activeData.areaName;

      if (overData?.type === 'task') {
        // Reordering tasks within the same project
        const overTask = overData.task;
        const overProjectId = overData.projectId;
        const overAreaName = overData.areaName;

        if (activeProjectId === overProjectId && activeAreaName === overAreaName) {
          // Find the project and get task indices
          let project: Project | undefined;
          if (activeAreaName) {
            const area = areas.find(a => a.name === activeAreaName);
            project = area?.projects.find(p => p.id === activeProjectId);
          } else {
            project = projects.find(p => p.id === activeProjectId);
          }

          if (project) {
            const oldIndex = project.tasks.findIndex(t => t.id === activeTask.id);
            const newIndex = project.tasks.findIndex(t => t.id === overTask.id);

            if (oldIndex !== -1 && newIndex !== -1) {
              reorderTasks(activeProjectId, activeAreaName, oldIndex, newIndex);
            }
          }
        } else {
          // Moving task to different project
          moveTask(
            activeTask.id,
            activeProjectId,
            overProjectId,
            activeAreaName,
            overAreaName
          );
        }
      }
    } else if (activeData?.type === 'project') {
      const activeProject = activeData.project;
      const activeAreaName = activeData.areaName;

      if (overData?.type === 'project') {
        const overProject = overData.project;
        const overAreaName = overData.areaName;

        if (activeAreaName === overAreaName && activeAreaName) {
          // Reordering projects within the same area
          const area = areas.find(a => a.name === activeAreaName);
          if (area) {
            const oldIndex = area.projects.findIndex(p => p.id === activeProject.id);
            const newIndex = area.projects.findIndex(p => p.id === overProject.id);

            if (oldIndex !== -1 && newIndex !== -1) {
              reorderProjects(activeAreaName, oldIndex, newIndex);
            }
          }
        } else if (!activeAreaName && !overAreaName) {
          // Reordering standalone projects
          const oldIndex = projects.findIndex(p => p.id === activeProject.id);
          const newIndex = projects.findIndex(p => p.id === overProject.id);

          if (oldIndex !== -1 && newIndex !== -1) {
            reorderStandaloneProjects(oldIndex, newIndex);
          }
        }
      }
    }
  }, [areas, projects, moveTask, reorderTasks, reorderProjects, reorderStandaloneProjects]);

  // Compute filterCounts using the new utility
  const allProjects = [
    ...projects.map(p => ({ project: p })),
    ...areas.flatMap(area => area.projects.map(p => ({ project: p, areaName: area.name })))
  ];

  const allTasks = [
    ...allProjects.flatMap(obj => {
      if ('areaName' in obj) {
        return obj.project.tasks.map(task => ({ ...task, project: obj.project, areaName: obj.areaName }));
      } else {
        return obj.project.tasks.map(task => ({ ...task, project: obj.project }));
      }
    })
  ];

  const filterCounts = calculateFilterCounts(allTasks, projects);

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
    >
      <div className="min-h-screen flex flex-col bg-[#181a1a]">
        <Header />
        <div className="flex flex-1">
          <Sidebar
            areas={areas}
            projects={projects}
            selectedKey={selectedKey}
            onSelect={setSelectedKey}
            onAddArea={handleAddArea}
            onAddProject={handleAddProject}
            onAreaNameChange={handleAreaNameChange}
            filterCounts={filterCounts}
          />
          <main className="flex-1 bg-[#181a1a]">
            <MainContent
              selectedKey={selectedKey}
              areas={areas}
              projects={projects}
              onToggleChecklist={handleToggleChecklist}
              onToggleTask={handleToggleTask}
              onAddTask={(projectId, areaName) => {
                setModalContext({ projectId, areaName });
                setShowTaskModal(true);
              }}
              onEditTask={(task, projectId, areaName) => {
                setEditingTask(task);
                setModalContext({ projectId, areaName });
                setShowTaskModal(true);
              }}
              onAddProject={(areaName) => {
                setModalContext({ areaName });
                setShowProjectModal(true);
              }}
              onEditProject={(project, areaName) => {
                setEditingProject(project);
                setModalContext({ areaName });
                setShowProjectModal(true);
              }}
              onAddArea={() => {
                setShowAreaModal(true);
              }}
              onEditArea={(area) => {
                setEditingArea(area);
                setShowAreaModal(true);
              }}
            />
          </main>
        </div>
      </div>

      {/* Modals */}
      <TaskModal
        open={showTaskModal}
        onClose={() => {
          setShowTaskModal(false);
          setEditingTask(undefined);
          setModalContext({});
        }}
        onSave={handleSaveTask}
        task={editingTask}
        projectId={modalContext.projectId}
        areaId={modalContext.areaId}
      />

      <ProjectModal
        open={showProjectModal}
        onClose={() => {
          setShowProjectModal(false);
          setEditingProject(undefined);
          setModalContext({});
        }}
        onSave={handleSaveProject}
        project={editingProject}
        areaId={modalContext.areaId}
      />

      <AreaModal
        open={showAreaModal}
        onClose={() => {
          setShowAreaModal(false);
          setEditingArea(undefined);
        }}
        onSave={handleSaveArea}
        area={editingArea}
      />
    </DndContext>
  );
}
