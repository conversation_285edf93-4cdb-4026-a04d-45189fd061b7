'use client';
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import MainContent from "@/components/MainContent";
import React, { useState, useCallback } from "react";
import { areas as mockAreas, projects as mockProjects, Area, Project, Task } from "../mockData";
import { calculateFilterCounts } from "../utils/filterDefinitions";
import { useTaskActions } from "../hooks/useTaskActions";


export default function Home() {
  // Move sidebar state up so it can be shared
  const [selectedKey, setSelectedKey] = useState<string>("filter:Today");
  const [areas, setAreas] = useState<Area[]>(mockAreas);
  const [projects, setProjects] = useState<Project[]>(mockProjects);

  // Helper to generate a unique id
  const generateId = (prefix: string = "id"): string => `${prefix}_${Math.random().toString(36).substr(2, 9)}`;

  // Add new area and select it
  const handleAddArea = () => {
    const newAreaName = `New Area`;
    const newArea: Area = {
      id: generateId('area'),
      name: newAreaName,
      projects: [],
    };
    setAreas(prev => [...prev, newArea]);
    setSelectedKey(`area:${newAreaName}`);
  };

  // Update area name
  const handleAreaNameChange = (areaKey: string, newName: string) => {
    setAreas(prev => prev.map(area =>
      `area:${area.name}` === areaKey ? { ...area, name: newName } : area
    ));
    // If the selectedKey was the old name, update it
    if (selectedKey === areaKey) {
      setSelectedKey(`area:${newName}`);
    }
  };

  // Add new project and select it
  const handleAddProject = () => {
    // Helper to generate a unique project name
    const getUniqueProjectName = (baseName: string, existingNames: string[]) => {
      if (!existingNames.includes(baseName)) return baseName;
      let i = 2;
      let newName = `${baseName} ${i}`;
      while (existingNames.includes(newName)) {
        i++;
        newName = `${baseName} ${i}`;
      }
      return newName;
    };

    // 1. Area or area project selected
    if (selectedKey.startsWith("area:")) {
      const areaProjectKey = selectedKey.slice(5).trim();
      let matchedArea: Area | undefined = undefined;
      let matchedAreaName = '';
      for (const area of areas) {
        if (areaProjectKey.startsWith(area.name)) {
          if (!matchedArea || area.name.length > matchedAreaName.length) {
            matchedArea = area;
            matchedAreaName = area.name;
          }
        }
      }
      if (matchedArea) {
        const existingNames = matchedArea.projects.map(p => p.name);
        const uniqueName = getUniqueProjectName("New Project", existingNames);
        const newProject: Project = {
          id: generateId('project'),
          name: uniqueName,
          tasks: [],
        };
        setAreas(prev => prev.map(area =>
          area.name === matchedAreaName
            ? { ...area, projects: [...area.projects, newProject] }
            : area
        ));
        setSelectedKey(`area:${matchedAreaName} ${uniqueName}`);
        return;
      }
    }
    // 2. Otherwise, add to standalone projects
    const existingNames = projects.map(p => p.name);
    const uniqueName = getUniqueProjectName("New Project", existingNames);
    const newProject: Project = {
      id: generateId('project'),
      name: uniqueName,
      tasks: [],
    };
    setProjects(prev => [...prev, newProject]);
    setSelectedKey(`project:${uniqueName}`);
  };

  // Use the task actions hook
  const { handleToggleTask, handleToggleChecklist } = useTaskActions({
    setAreas,
    setProjects
  });

  // Compute filterCounts using the new utility
  const allProjects = [
    ...projects.map(p => ({ project: p })),
    ...areas.flatMap(area => area.projects.map(p => ({ project: p, areaName: area.name })))
  ];

  const allTasks = [
    ...allProjects.flatMap(obj => {
      if ('areaName' in obj) {
        return obj.project.tasks.map(task => ({ ...task, project: obj.project, areaName: obj.areaName }));
      } else {
        return obj.project.tasks.map(task => ({ ...task, project: obj.project }));
      }
    })
  ];

  const filterCounts = calculateFilterCounts(allTasks, projects);

  return (
    <div className="min-h-screen flex flex-col bg-[#181a1a]">
      <Header />
      <div className="flex flex-1">
        <Sidebar
          areas={areas}
          projects={projects}
          selectedKey={selectedKey}
          onSelect={setSelectedKey}
          onAddArea={handleAddArea}
          onAddProject={handleAddProject}
          onAreaNameChange={handleAreaNameChange}
          filterCounts={filterCounts}
        />
        <main className="flex-1 bg-[#181a1a]">
          <MainContent
            selectedKey={selectedKey}
            areas={areas}
            projects={projects}
            onToggleChecklist={handleToggleChecklist}
            onToggleTask={handleToggleTask}
          />
        </main>
      </div>
    </div>
  );
}
