import { useCallback } from 'react';
import { Area, Project, Task } from '../mockData';

interface UseDragDropProps {
  areas: Area[];
  projects: Project[];
  setAreas: React.Dispatch<React.SetStateAction<Area[]>>;
  setProjects: React.Dispatch<React.SetStateAction<Project[]>>;
}

export function useDragDrop({ areas, projects, setAreas, setProjects }: UseDragDropProps) {
  
  const moveTask = useCallback((
    taskId: string,
    sourceProjectId: string,
    targetProjectId: string,
    sourceAreaName?: string,
    targetAreaName?: string,
    newIndex?: number
  ) => {


    // Case 1: Both source and target are in areas
    if (sourceAreaName && targetAreaName) {
      setAreas(prev => {
        let taskToMove: Task | null = null;

        // First pass: find and remove the task from source
        const updatedAreas = prev.map(area => {
          if (area.name === sourceAreaName) {
            return {
              ...area,
              projects: area.projects.map(project => {
                if (project.id === sourceProjectId) {
                  taskToMove = project.tasks.find(t => t.id === taskId) || null;
                  return { ...project, tasks: project.tasks.filter(t => t.id !== taskId) };
                }
                return project;
              })
            };
          }
          return area;
        });

        if (!taskToMove) {
          return prev;
        }

        // Second pass: add task to target
        return updatedAreas.map(area => {
          if (area.name === targetAreaName) {
            return {
              ...area,
              projects: area.projects.map(project => {
                if (project.id === targetProjectId) {
                  const newTasks = [...project.tasks];
                  if (newIndex !== undefined && newIndex >= 0) {
                    newTasks.splice(newIndex, 0, taskToMove!);
                  } else {
                    newTasks.push(taskToMove!);
                  }
                  return { ...project, tasks: newTasks };
                }
                return project;
              })
            };
          }
          return area;
        });
      });
    }
    // Case 2: Source in area, target in standalone projects
    else if (sourceAreaName && !targetAreaName) {
      let taskToMove: Task | null = null;

      // Remove from area
      setAreas(prev => prev.map(area => {
        if (area.name === sourceAreaName) {
          return {
            ...area,
            projects: area.projects.map(project => {
              if (project.id === sourceProjectId) {
                taskToMove = project.tasks.find(t => t.id === taskId) || null;
                return { ...project, tasks: project.tasks.filter(t => t.id !== taskId) };
              }
              return project;
            })
          };
        }
        return area;
      }));

      // Add to standalone project
      if (taskToMove) {
        setProjects(prev => prev.map(project => {
          if (project.id === targetProjectId) {
            const newTasks = [...project.tasks];
            if (newIndex !== undefined && newIndex >= 0) {
              newTasks.splice(newIndex, 0, taskToMove!);
            } else {
              newTasks.push(taskToMove!);
            }
            return { ...project, tasks: newTasks };
          }
          return project;
        }));
      }
    }
    // Case 3: Source in standalone projects, target in area
    else if (!sourceAreaName && targetAreaName) {
      let taskToMove: Task | null = null;

      // Remove from standalone project
      setProjects(prev => prev.map(project => {
        if (project.id === sourceProjectId) {
          taskToMove = project.tasks.find(t => t.id === taskId) || null;
          return { ...project, tasks: project.tasks.filter(t => t.id !== taskId) };
        }
        return project;
      }));

      // Add to area project
      if (taskToMove) {
        setAreas(prev => prev.map(area => {
          if (area.name === targetAreaName) {
            return {
              ...area,
              projects: area.projects.map(project => {
                if (project.id === targetProjectId) {
                  const newTasks = [...project.tasks];
                  if (newIndex !== undefined && newIndex >= 0) {
                    newTasks.splice(newIndex, 0, taskToMove!);
                  } else {
                    newTasks.push(taskToMove!);
                  }
                  return { ...project, tasks: newTasks };
                }
                return project;
              })
            };
          }
          return area;
        }));
      }
    }
    // Case 4: Both source and target are standalone projects
    else if (!sourceAreaName && !targetAreaName) {
      setProjects(prev => {
        let taskToMove: Task | null = null;

        // First pass: find and remove the task from source
        const updatedProjects = prev.map(project => {
          if (project.id === sourceProjectId) {
            taskToMove = project.tasks.find(t => t.id === taskId) || null;
            return { ...project, tasks: project.tasks.filter(t => t.id !== taskId) };
          }
          return project;
        });

        if (!taskToMove) {
          return prev;
        }

        // Second pass: add task to target
        return updatedProjects.map(project => {
          if (project.id === targetProjectId) {
            const newTasks = [...project.tasks];
            if (newIndex !== undefined && newIndex >= 0) {
              newTasks.splice(newIndex, 0, taskToMove!);
            } else {
              newTasks.push(taskToMove!);
            }
            return { ...project, tasks: newTasks };
          }
          return project;
        });
      });
    }
  }, [setAreas, setProjects]);

  const reorderTasks = useCallback((
    projectId: string,
    areaName: string | undefined,
    oldIndex: number,
    newIndex: number
  ) => {
    if (areaName) {
      setAreas(prev => prev.map(area => {
        if (area.name === areaName) {
          return {
            ...area,
            projects: area.projects.map(project => {
              if (project.id === projectId) {
                const newTasks = [...project.tasks];
                const [movedTask] = newTasks.splice(oldIndex, 1);
                newTasks.splice(newIndex, 0, movedTask);
                return { ...project, tasks: newTasks };
              }
              return project;
            })
          };
        }
        return area;
      }));
    } else {
      setProjects(prev => prev.map(project => {
        if (project.id === projectId) {
          const newTasks = [...project.tasks];
          const [movedTask] = newTasks.splice(oldIndex, 1);
          newTasks.splice(newIndex, 0, movedTask);
          return { ...project, tasks: newTasks };
        }
        return project;
      }));
    }
  }, [setAreas, setProjects]);

  const reorderProjects = useCallback((
    areaName: string,
    oldIndex: number,
    newIndex: number
  ) => {
    setAreas(prev => prev.map(area => {
      if (area.name === areaName) {
        const newProjects = [...area.projects];
        const [movedProject] = newProjects.splice(oldIndex, 1);
        newProjects.splice(newIndex, 0, movedProject);
        return { ...area, projects: newProjects };
      }
      return area;
    }));
  }, [setAreas]);

  const reorderStandaloneProjects = useCallback((
    oldIndex: number,
    newIndex: number
  ) => {
    setProjects(prev => {
      const newProjects = [...prev];
      const [movedProject] = newProjects.splice(oldIndex, 1);
      newProjects.splice(newIndex, 0, movedProject);
      return newProjects;
    });
  }, [setProjects]);

  return {
    moveTask,
    reorderTasks,
    reorderProjects,
    reorderStandaloneProjects
  };
}
