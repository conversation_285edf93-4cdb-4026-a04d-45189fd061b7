import { useCallback } from 'react';
import { Area, Project, Task } from '../mockData';

interface UseDragDropProps {
  areas: Area[];
  projects: Project[];
  setAreas: React.Dispatch<React.SetStateAction<Area[]>>;
  setProjects: React.Dispatch<React.SetStateAction<Project[]>>;
}

export function useDragDrop({ areas, projects, setAreas, setProjects }: UseDragDropProps) {
  
  const moveTask = useCallback((
    taskId: string,
    sourceProjectId: string,
    targetProjectId: string,
    sourceAreaName?: string,
    targetAreaName?: string,
    newIndex?: number
  ) => {
    // Find the task to move
    let taskToMove: Task | null = null;
    
    // Remove task from source
    if (sourceAreaName) {
      setAreas(prev => prev.map(area => {
        if (area.name === sourceAreaName) {
          const sourceProject = area.projects.find(p => p.id === sourceProjectId);
          if (sourceProject) {
            taskToMove = sourceProject.tasks.find(t => t.id === taskId) || null;
            return {
              ...area,
              projects: area.projects.map(project =>
                project.id === sourceProjectId
                  ? { ...project, tasks: project.tasks.filter(t => t.id !== taskId) }
                  : project
              )
            };
          }
        }
        return area;
      }));
    } else {
      setProjects(prev => prev.map(project => {
        if (project.id === sourceProjectId) {
          taskToMove = project.tasks.find(t => t.id === taskId) || null;
          return { ...project, tasks: project.tasks.filter(t => t.id !== taskId) };
        }
        return project;
      }));
    }

    if (!taskToMove) return;

    // Add task to target
    if (targetAreaName) {
      setAreas(prev => prev.map(area => {
        if (area.name === targetAreaName) {
          return {
            ...area,
            projects: area.projects.map(project => {
              if (project.id === targetProjectId) {
                const newTasks = [...project.tasks];
                if (newIndex !== undefined) {
                  newTasks.splice(newIndex, 0, taskToMove!);
                } else {
                  newTasks.push(taskToMove!);
                }
                return { ...project, tasks: newTasks };
              }
              return project;
            })
          };
        }
        return area;
      }));
    } else {
      setProjects(prev => prev.map(project => {
        if (project.id === targetProjectId) {
          const newTasks = [...project.tasks];
          if (newIndex !== undefined) {
            newTasks.splice(newIndex, 0, taskToMove!);
          } else {
            newTasks.push(taskToMove!);
          }
          return { ...project, tasks: newTasks };
        }
        return project;
      }));
    }
  }, [setAreas, setProjects]);

  const reorderTasks = useCallback((
    projectId: string,
    areaName: string | undefined,
    oldIndex: number,
    newIndex: number
  ) => {
    if (areaName) {
      setAreas(prev => prev.map(area => {
        if (area.name === areaName) {
          return {
            ...area,
            projects: area.projects.map(project => {
              if (project.id === projectId) {
                const newTasks = [...project.tasks];
                const [movedTask] = newTasks.splice(oldIndex, 1);
                newTasks.splice(newIndex, 0, movedTask);
                return { ...project, tasks: newTasks };
              }
              return project;
            })
          };
        }
        return area;
      }));
    } else {
      setProjects(prev => prev.map(project => {
        if (project.id === projectId) {
          const newTasks = [...project.tasks];
          const [movedTask] = newTasks.splice(oldIndex, 1);
          newTasks.splice(newIndex, 0, movedTask);
          return { ...project, tasks: newTasks };
        }
        return project;
      }));
    }
  }, [setAreas, setProjects]);

  const reorderProjects = useCallback((
    areaName: string,
    oldIndex: number,
    newIndex: number
  ) => {
    setAreas(prev => prev.map(area => {
      if (area.name === areaName) {
        const newProjects = [...area.projects];
        const [movedProject] = newProjects.splice(oldIndex, 1);
        newProjects.splice(newIndex, 0, movedProject);
        return { ...area, projects: newProjects };
      }
      return area;
    }));
  }, [setAreas]);

  const reorderStandaloneProjects = useCallback((
    oldIndex: number,
    newIndex: number
  ) => {
    setProjects(prev => {
      const newProjects = [...prev];
      const [movedProject] = newProjects.splice(oldIndex, 1);
      newProjects.splice(newIndex, 0, movedProject);
      return newProjects;
    });
  }, [setProjects]);

  return {
    moveTask,
    reorderTasks,
    reorderProjects,
    reorderStandaloneProjects
  };
}
