import { useCallback } from 'react';
import { Area, Project } from '../mockData';

interface UseTaskActionsProps {
  setAreas: React.Dispatch<React.SetStateAction<Area[]>>;
  setProjects: React.Dispatch<React.SetStateAction<Project[]>>;
}

export function useTaskActions({ setAreas, setProjects }: UseTaskActionsProps) {
  const handleToggleTask = useCallback((
    projectId: string, 
    taskId: string, 
    inArea: boolean, 
    areaName?: string
  ) => {
    if (inArea && areaName) {
      setAreas(prev => prev.map(area =>
        area.name === areaName
          ? {
              ...area,
              projects: area.projects.map(project =>
                project.id === projectId
                  ? {
                      ...project,
                      tasks: project.tasks.map(task =>
                        task.id === taskId
                          ? { ...task, completed: !task.completed }
                          : task
                      )
                    }
                  : project
              )
            }
          : area
      ));
    } else {
      setProjects(prev => prev.map(project =>
        project.id === projectId
          ? {
              ...project,
              tasks: project.tasks.map(task =>
                task.id === taskId
                  ? { ...task, completed: !task.completed }
                  : task
              )
            }
          : project
      ));
    }
  }, [setAreas, setProjects]);

  const handleToggleChecklist = useCallback((
    projectId: string, 
    taskId: string, 
    checklistId: string, 
    inArea: boolean, 
    areaName?: string
  ) => {
    if (inArea && areaName) {
      setAreas(prev => prev.map(area =>
        area.name === areaName
          ? {
              ...area,
              projects: area.projects.map(project =>
                project.id === projectId
                  ? {
                      ...project,
                      tasks: project.tasks.map(task =>
                        task.id === taskId
                          ? {
                              ...task,
                              checklist: task.checklist?.map(item =>
                                item.id === checklistId ? { ...item, completed: !item.completed } : item
                              )
                            }
                          : task
                      )
                    }
                  : project
              )
            }
          : area
      ));
    } else {
      setProjects(prev => prev.map(project =>
        project.id === projectId
          ? {
              ...project,
              tasks: project.tasks.map(task =>
                task.id === taskId
                  ? {
                      ...task,
                      checklist: task.checklist?.map(item =>
                        item.id === checklistId ? { ...item, completed: !item.completed } : item
                      )
                    }
                  : task
              )
            }
          : project
      ));
    }
  }, [setAreas, setProjects]);

  return {
    handleToggleTask,
    handleToggleChecklist
  };
}
