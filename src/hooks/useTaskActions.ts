import { useCallback } from 'react';
import { Area, Project, Task } from '../mockData';

interface UseTaskActionsProps {
  setAreas: React.Dispatch<React.SetStateAction<Area[]>>;
  setProjects: React.Dispatch<React.SetStateAction<Project[]>>;
}

export function useTaskActions({ setAreas, setProjects }: UseTaskActionsProps) {
  const handleToggleTask = useCallback((
    projectId: string, 
    taskId: string, 
    inArea: boolean, 
    areaName?: string
  ) => {
    if (inArea && areaName) {
      setAreas(prev => prev.map(area =>
        area.name === areaName
          ? {
              ...area,
              projects: area.projects.map(project =>
                project.id === projectId
                  ? {
                      ...project,
                      tasks: project.tasks.map(task =>
                        task.id === taskId
                          ? { ...task, completed: !task.completed }
                          : task
                      )
                    }
                  : project
              )
            }
          : area
      ));
    } else {
      setProjects(prev => prev.map(project =>
        project.id === projectId
          ? {
              ...project,
              tasks: project.tasks.map(task =>
                task.id === taskId
                  ? { ...task, completed: !task.completed }
                  : task
              )
            }
          : project
      ));
    }
  }, [setAreas, setProjects]);

  const handleToggleChecklist = useCallback((
    projectId: string, 
    taskId: string, 
    checklistId: string, 
    inArea: boolean, 
    areaName?: string
  ) => {
    if (inArea && areaName) {
      setAreas(prev => prev.map(area =>
        area.name === areaName
          ? {
              ...area,
              projects: area.projects.map(project =>
                project.id === projectId
                  ? {
                      ...project,
                      tasks: project.tasks.map(task =>
                        task.id === taskId
                          ? {
                              ...task,
                              checklist: task.checklist?.map(item =>
                                item.id === checklistId ? { ...item, completed: !item.completed } : item
                              )
                            }
                          : task
                      )
                    }
                  : project
              )
            }
          : area
      ));
    } else {
      setProjects(prev => prev.map(project =>
        project.id === projectId
          ? {
              ...project,
              tasks: project.tasks.map(task =>
                task.id === taskId
                  ? {
                      ...task,
                      checklist: task.checklist?.map(item =>
                        item.id === checklistId ? { ...item, completed: !item.completed } : item
                      )
                    }
                  : task
              )
            }
          : project
      ));
    }
  }, [setAreas, setProjects]);

  const handleAddTask = useCallback((
    taskData: Omit<Task, 'id'>,
    projectId: string,
    inArea: boolean,
    areaName?: string
  ) => {
    const newTask: Task = {
      ...taskData,
      id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };

    if (inArea && areaName) {
      setAreas(prev => prev.map(area =>
        area.name === areaName
          ? {
              ...area,
              projects: area.projects.map(project =>
                project.id === projectId
                  ? { ...project, tasks: [...project.tasks, newTask] }
                  : project
              )
            }
          : area
      ));
    } else {
      setProjects(prev => prev.map(project =>
        project.id === projectId
          ? { ...project, tasks: [...project.tasks, newTask] }
          : project
      ));
    }
  }, [setAreas, setProjects]);

  const handleUpdateTask = useCallback((
    taskId: string,
    taskData: Omit<Task, 'id'>,
    projectId: string,
    inArea: boolean,
    areaName?: string
  ) => {
    const updatedTask: Task = { ...taskData, id: taskId };

    if (inArea && areaName) {
      setAreas(prev => prev.map(area =>
        area.name === areaName
          ? {
              ...area,
              projects: area.projects.map(project =>
                project.id === projectId
                  ? {
                      ...project,
                      tasks: project.tasks.map(task =>
                        task.id === taskId ? updatedTask : task
                      )
                    }
                  : project
              )
            }
          : area
      ));
    } else {
      setProjects(prev => prev.map(project =>
        project.id === projectId
          ? {
              ...project,
              tasks: project.tasks.map(task =>
                task.id === taskId ? updatedTask : task
              )
            }
          : project
      ));
    }
  }, [setAreas, setProjects]);

  return {
    handleToggleTask,
    handleToggleChecklist,
    handleAddTask,
    handleUpdateTask
  };
}
