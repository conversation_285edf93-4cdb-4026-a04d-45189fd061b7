// Utility for robust local date parsing from ISO strings

/**
 * Parses an ISO date string (YYYY-MM-DD) as a local date (midnight local time).
 * @param iso ISO date string (YYYY-MM-DD)
 * @returns Date object at local midnight
 */
export function parseLocalDateFromISO(iso: string): Date {
  const [year, month, day] = iso.split('-').map(Number);
  return new Date(year, month - 1, day);
}

/**
 * Checks if a date matches today
 * @param dateStr ISO date string
 * @returns true if the date is today
 */
export function isToday(dateStr: string): boolean {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const date = parseLocalDateFromISO(dateStr);
  date.setHours(0, 0, 0, 0);
  return date.getTime() === today.getTime();
}

/**
 * Checks if a date is in the future (after today)
 * @param dateStr ISO date string
 * @returns true if the date is after today
 */
export function isFuture(dateStr: string): boolean {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const date = parseLocalDateFromISO(dateStr);
  date.setHours(0, 0, 0, 0);
  return date.getTime() > today.getTime();
}

/**
 * Checks if a repeating task should appear today
 * @param repeating Repeating rule object
 * @param startDate Optional start date for the repeating task
 * @returns true if the task should appear today based on its repeating rule
 */
export function shouldRepeatToday(repeating: any, startDate?: string): boolean {
  // Simplified implementation - in a real app this would be much more complex
  const today = new Date();

  if (repeating.frequency === 'daily') {
    return true; // Daily tasks always appear
  }

  if (repeating.frequency === 'weekly' && repeating.daysOfWeek) {
    return repeating.daysOfWeek.includes(today.getDay());
  }

  // For monthly/yearly, we'd need more complex logic
  // This is a simplified version
  return false;
}

/**
 * Checks if a task should appear in the Today filter
 * @param task Task object
 * @returns true if the task should appear in Today
 */
export function isTodayTask(task: any): boolean {
  if (task.completed) return false;

  // Check start date
  if (task.startDate && isToday(task.startDate)) {
    return true;
  }

  // Check deadline (dueDate)
  if (task.dueDate && isToday(task.dueDate)) {
    return true;
  }

  // Check repeating rules
  if (task.repeating && shouldRepeatToday(task.repeating, task.startDate)) {
    return true;
  }

  return false;
}
