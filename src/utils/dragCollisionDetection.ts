import { CollisionDetection, closestCorners } from '@dnd-kit/core';

/**
 * Custom collision detection that prioritizes project drop zones when dragging tasks
 * between different projects, but uses normal collision detection for same-project reordering
 */
export const customCollisionDetection: CollisionDetection = (args) => {
  // Use standard collision detection for now to debug
  const collisions = closestCorners(args);

  console.log('Collision detection:', {
    active: args.active.id,
    collisions: collisions?.map(c => ({ id: c.id, data: c.data?.current }))
  });

  return collisions;
};
