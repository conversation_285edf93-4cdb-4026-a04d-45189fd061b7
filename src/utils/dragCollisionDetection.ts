import { CollisionDetection, closestCorners, getFirstCollision } from '@dnd-kit/core';

/**
 * Custom collision detection that prioritizes project drop zones when dragging tasks
 * between different projects, but uses normal collision detection for same-project reordering
 */
export const customCollisionDetection: CollisionDetection = (args) => {
  const { active, droppableContainers } = args;
  
  // Check if we're dragging a task
  const isDraggingTask = active.data.current?.type === 'task';
  
  if (!isDraggingTask) {
    // For non-task items (like projects), use default collision detection
    return closestCorners(args);
  }

  const activeProjectId = active.data.current?.projectId;
  const activeAreaName = active.data.current?.areaName;

  // Get all droppable containers
  const droppableArray = Array.from(droppableContainers.values());
  
  // Separate project drop zones from task containers
  const projectDropZones = droppableArray.filter(container => 
    container.data.current?.type === 'project-drop'
  );
  
  const taskContainers = droppableArray.filter(container => 
    container.data.current?.type === 'task' || !container.data.current?.type
  );

  // Check for collisions with project drop zones first (for cross-project moves)
  const projectCollisions = projectDropZones
    .filter(container => {
      const projectData = container.data.current;
      // Only consider drop zones for different projects
      return projectData?.project?.id !== activeProjectId || 
             projectData?.areaName !== activeAreaName;
    })
    .map(container => {
      const rect = container.rect.current;
      if (!rect) return null;
      
      // Calculate collision with the project drop zone
      const collision = getFirstCollision([container], args.collisionRect);
      return collision;
    })
    .filter(Boolean);

  // If we have project collisions, prioritize them
  if (projectCollisions.length > 0) {
    return projectCollisions;
  }

  // Otherwise, use normal collision detection for task reordering
  return closestCorners({
    ...args,
    droppableContainers: new Map(
      taskContainers.map(container => [container.id, container])
    )
  });
};
