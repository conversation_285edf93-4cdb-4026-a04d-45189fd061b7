import { CollisionDetection, closestCorners } from '@dnd-kit/core';

/**
 * Custom collision detection that prioritizes project drop zones when dragging tasks
 * between different projects, but uses normal collision detection for same-project reordering
 */
export const customCollisionDetection: CollisionDetection = (args) => {
  // Use standard collision detection - works reliably
  return closestCorners(args);
};
