import { Task, Project } from "../mockData";
import { isToday, isFuture, shouldRepeatToday } from "./date";

export type FilterFunction = (task: Task & { project: Project; areaName?: string }) => boolean;

export interface FilterDefinition {
  key: string;
  label: string;
  filterFn: FilterFunction;
  specialHandling?: 'inbox' | 'today';
}

export const filterDefinitions: Record<string, FilterDefinition> = {
  'filter:Inbox': {
    key: 'filter:Inbox',
    label: 'Inbox',
    filterFn: () => false, // Special handling in component
    specialHandling: 'inbox'
  },
  
  'filter:Today': {
    key: 'filter:Today',
    label: 'Today',
    filterFn: (t) => {
      if (t.completed) return false;
      
      // Check start date
      if (t.startDate && isToday(t.startDate)) {
        return true;
      }
      
      // Check deadline (dueDate)
      if (t.dueDate && isToday(t.dueDate)) {
        return true;
      }
      
      // Check repeating rules
      if (t.repeating && shouldRepeatToday(t.repeating, t.startDate)) {
        return true;
      }
      
      return false;
    },
    specialHandling: 'today'
  },
  
  'filter:Upcoming': {
    key: 'filter:Upcoming',
    label: 'Upcoming',
    filterFn: (t) => {
      if (t.completed) return false;
      if (t.startDate && isFuture(t.startDate)) {
        return true;
      }
      return false;
    }
  },
  
  'filter:Anytime': {
    key: 'filter:Anytime',
    label: 'Anytime',
    filterFn: (t) => {
      if (t.completed) return false;
      if (t.tags?.includes('someday')) return false;
      
      // Exclude tasks with future start dates (they belong in Upcoming)
      if (t.startDate && isFuture(t.startDate)) return false;
      
      // Include tasks with deadlines (they are active and can be done anytime)
      // Include tasks that start today or have no start date
      // Include tasks with past start dates
      return true;
    }
  },
  
  'filter:Someday': {
    key: 'filter:Someday',
    label: 'Someday',
    filterFn: (t) => {
      if (t.completed) return false;
      return !!(t.tags?.includes('someday'));
    }
  },
  
  'filter:Logbook': {
    key: 'filter:Logbook',
    label: 'Logbook',
    filterFn: (t) => !!t.completed
  }
};

export function getFilterDefinition(filterKey: string): FilterDefinition | undefined {
  return filterDefinitions[filterKey];
}

export function calculateFilterCounts(
  allTasks: (Task & { project: Project; areaName?: string })[],
  projects: Project[]
): Record<string, number> {
  const counts: Record<string, number> = {};
  
  // Special case for Inbox
  counts['filter:Inbox'] = projects.find(p => p.name === 'Inbox')?.tasks.filter(t => !t.completed).length ?? 0;
  
  // Calculate counts for other filters
  Object.values(filterDefinitions).forEach(filterDef => {
    if (filterDef.specialHandling !== 'inbox') {
      counts[filterDef.key] = allTasks.filter(filterDef.filterFn).length;
    }
  });
  
  return counts;
}
