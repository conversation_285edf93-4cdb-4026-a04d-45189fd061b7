// Mock data for a Things 3-inspired web app
// Covers areas, projects, tasks, checklist items, filters, and tags

// Dynamic date helpers for always-relevant mock tasks
function getISODate(offsetDays: number = 0): string {
  const d = new Date();
  d.setDate(d.getDate() + offsetDays);
  d.setHours(0,0,0,0);
  return d.toISOString().slice(0,10);
}

export type ChecklistItem = {
  id: string;
  text: string;
  completed: boolean;
};

export type Task = {
  id: string;
  title: string;
  notes?: string;
  dueDate?: string; // ISO date (deadline)
  startDate?: string; // ISO date (when task becomes active)
  thisEvening?: boolean; // For Today filter's "This Evening" section
  tags?: string[];
  checklist?: ChecklistItem[];
  completed: boolean;
  projectId?: string;
  areaId?: string;
  repeating?: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
    interval?: number; // e.g., every 2 weeks
    daysOfWeek?: number[]; // 0-6, Sunday=0
    dayOfMonth?: number; // 1-31
    monthOfYear?: number; // 1-12
  };
};

export type Project = {
  id: string;
  name: string;
  tasks: Task[];
  notes?: string;
  deadline?: string; // ISO date
  tags?: string[];
  checklist?: ChecklistItem[];
};

export type Area = {
  id: string;
  name: string;
  projects: Project[];
};

export type Filter = {
  key: string;
  label: string;
  icon: string;
};

// Filters (Things 3 inspired)
export const filters: Filter[] = [
  { key: 'filter:Today', label: 'Today', icon: '☀️' },
  { key: 'filter:Upcoming', label: 'Upcoming', icon: '📅' },
  { key: 'filter:Anytime', label: 'Anytime', icon: '🕓' },
  { key: 'filter:Someday', label: 'Someday', icon: '🌙' },
  { key: 'filter:Logbook', label: 'Logbook', icon: '📔' },
];

// Tags (global)
export const tags: string[] = ['work', 'personal', 'urgent', 'low-priority', 'home', 'fitness', 'shopping'];

// Mock checklist items
const checklist1: ChecklistItem[] = [
  { id: 'c1', text: 'Draft outline', completed: true },
  { id: 'c2', text: 'Write content', completed: false },
  { id: 'c3', text: 'Review', completed: false },
];

const checklist2: ChecklistItem[] = [
  { id: 'c4', text: 'Buy milk', completed: true },
  { id: 'c5', text: 'Buy bread', completed: false },
];

// Mock tasks
const tasksProject1: Task[] = [
  {
    id: 't1',
    title: 'Write project proposal',
    notes: 'Include goals, timeline, and resources.',
    startDate: getISODate(0), // Active today
    dueDate: '2025-06-01', // Deadline in future
    tags: ['work', 'urgent'],
    checklist: checklist1,
    completed: false,
  },
  {
    id: 't2',
    title: 'Email team',
    notes: 'Send out the agenda for next week.',
    startDate: getISODate(2), // Starts in 2 days
    dueDate: '2025-06-02', // Deadline in future
    tags: ['work'],
    completed: false,
  },
];

const tasksProject2: Task[] = [
  {
    id: 't3',
    title: 'Grocery shopping',
    notes: 'Check for discounts.',
    startDate: getISODate(0), // Active today
    tags: ['shopping', 'personal'],
    checklist: checklist2,
    completed: false,
  },
  {
    id: 't4',
    title: 'Clean kitchen',
    completed: true,
    tags: ['home'],
  },
];

const tasksProject3: Task[] = [
  {
    id: 't5',
    title: 'Morning run',
    startDate: getISODate(0), // Active today
    dueDate: '2025-06-01', // Deadline in future
    tags: ['fitness'],
    completed: false,
  },
  {
    id: 't6',
    title: 'Plan vacation',
    startDate: getISODate(3), // Starts in 3 days (upcoming)
    tags: ['personal'],
    completed: false,
  },
  {
    id: 't7',
    title: 'Learn photography',
    tags: ['someday', 'personal'],
    completed: false,
  },
];

// Standalone projects (not in any area)
export const projects: Project[] = [
  {
    id: 'inbox',
    name: 'Inbox',
    tasks: [
      {
        id: 'inbox1',
        title: 'Inbox: Buy batteries',
        completed: false,
        notes: 'For the remote control',
      },
      {
        id: 'inbox2',
        title: 'Inbox: Read Things 3 support article',
        completed: false,
        notes: 'Understand how filters work',
      },
      {
        id: 'today1',
        title: 'Today: Pay rent',
        completed: false,
        startDate: getISODate(0), // Starts today
        dueDate: getISODate(0), // Also due today
        notes: 'Due by end of today',
      },
      {
        id: 'evening1',
        title: 'This Evening: Review emails',
        completed: false,
        startDate: getISODate(0), // Starts today
        thisEvening: true,
        notes: 'Handle after work hours',
      },
    ],
  },
  {
    id: 'p1',
    name: 'Personal Errands',
    tasks: tasksProject2,
    tags: ['personal'],
  },
  {
    id: 'p2',
    name: 'Fitness Goals',
    tasks: tasksProject3,
    notes: 'Track weekly progress.',
    tags: ['fitness'],
  }
];

// Areas (each with projects)
export const areas: Area[] = [
  {
    id: 'a1',
    name: 'Work',
    projects: [
      {
        id: 'p3',
        name: 'Q2 Planning',
        tasks: tasksProject1,
        notes: 'Quarterly planning for team.',
        deadline: '2025-06-10',
        tags: ['work'],
      },
      {
        id: 'p4',
        name: 'Team Building',
        tasks: [], // No tasks yet
        tags: ['work'],
      },
    ],
  },
  {
    id: 'a2',
    name: 'Home',
    projects: [], // Area with no projects
  },
];
